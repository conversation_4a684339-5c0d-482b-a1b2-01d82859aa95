# Socket.IO 聊天系统实现指南

## 🚀 系统概述

本项目实现了基于 Socket.IO 的实时聊天系统，前端使用 `socket.io-client`，后端使用 `netty-socketio`。

## 📋 实现的功能

- ✅ 实时消息发送和接收
- ⚠️ JWT 身份验证（暂时禁用用于测试）
- ✅ 聊天历史记录持久化
- ✅ 未读消息计数
- ✅ 自动重连机制
- ✅ 房间管理（私聊）
- ✅ 响应式 UI 设计
- ✅ Socket.IO 连接测试页面

## ⚠️ 重要说明

**当前版本为测试版本，已暂时禁用JWT认证以便于测试连接。在生产环境中请启用认证功能。**

## 🛠️ 安装和配置

### 1. 数据库设置

首先需要创建消息表，请在 MySQL 数据库中执行以下 SQL：

```sql
-- 创建消息表
CREATE TABLE IF NOT EXISTS message (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    sender_id BIGINT NOT NULL,
    recipient_id BIGINT NOT NULL,
    content TEXT,
    images TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_sender_id (sender_id),
    INDEX idx_recipient_id (recipient_id),
    INDEX idx_create_time (create_time),
    INDEX idx_sender_recipient (sender_id, recipient_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**可选：插入测试数据**
```sql
-- 执行测试数据插入脚本
source celeste-server/src/main/resources/db/migration/insert_test_messages.sql
```

### 2. 后端配置

Socket.IO 服务器配置已添加到 `application.yml`：

```yaml
socketio:
  host: localhost
  port: 9092
  bossCount: 1
  workCount: 100
  allowCustomRequests: true
  upgradeTimeout: 1000000
  pingTimeout: 6000000
  pingInterval: 25000000
```

### 3. 编译项目

由于修改了 Message 实体（从 @OneToOne 改为 @ManyToOne），需要重新编译项目以生成 Jimmer 相关类：

```bash
# 在项目根目录执行
mvn clean compile
```

### 4. 测试 Socket.IO 连接

访问测试页面验证连接：
- 启动后端服务器
- 启动前端应用
- 访问 `http://localhost:3000/socket-test` 测试Socket.IO连接

## 🔧 修复的问题

### 1. 实体关系修正
- 将 Message 实体中的 `@OneToOne` 改为 `@ManyToOne`
- 修正了 MessageDraft 的使用方式

### 2. 编译错误修复
- 修复了 `MessageDraft.$.produce()` 返回类型问题
- 移除了不支持的 SQL 方法调用

### 3. Socket.IO 认证问题修复
- 修复了 `AuthorizationListener` 接口兼容性问题
- 暂时禁用认证以便测试连接
- 添加了详细的连接日志

### 4. 添加测试功能
- 创建了 Socket.IO 连接测试页面
- 支持无认证连接（测试模式）
- 实时连接状态监控

### 5. 创建缺失的前端组件
- **Chat.vue**: 聊天消息气泡组件，支持文本和图片显示
- **Image.vue**: 图片显示组件，支持预览和懒加载
- **SocketTestView.vue**: Socket.IO 连接测试页面
- **default-avatar.svg**: 默认头像图标

### 6. 安全性改进
- 添加了空值检查（?.操作符）
- 改进了错误处理机制
- 添加了默认头像回退

### 7. 聊天列表去重修复
- **后端去重**: 使用Java代码在应用层进行去重（兼容Jimmer ORM）
- **前端去重**: 添加额外的去重逻辑作为保险
- **UI优化**: 简化聊天列表显示逻辑，提高可读性
- **测试数据**: 提供测试消息插入脚本
- **编译修复**: 修复了Jimmer SQL查询语法错误和实体方法调用问题

## 🧪 测试步骤

### 1. 启动后端服务器

启动 Spring Boot 应用，确保看到以下日志：

```
Socket.IO 服务器配置完成，端口: 9092
Socket.IO 服务器启动成功，端口: 9092
```

### 2. 测试 REST API

访问测试端点确认服务正常：

```bash
curl http://localhost:8080/api/message/status
# 应该返回: "Chat system is running"

# 调试聊天列表去重（可选）
curl http://localhost:8080/api/message/debug/raw/1
# 查看用户1的原始聊天数据

curl http://localhost:8080/api/message/debug/unique/1
# 查看用户1去重后的聊天列表

curl http://localhost:8080/api/message/debug/simple/1
# 查看用户1简化版去重的聊天列表

curl http://localhost:8080/api/message/recent
# 查看当前用户的聊天列表（需要认证）
```

### 3. 启动前端应用

```bash
cd celeste-web
npm run dev
```

### 4. 测试 Socket.IO 连接

1. 访问 `http://localhost:3000/socket-test`
2. 查看连接状态是否为"已连接"
3. 发送测试消息验证通信
4. 检查浏览器控制台的连接日志

### 5. 测试聊天功能

1. 登录应用（可选，当前支持无认证测试）
2. 点击侧边栏的"聊天"选项
3. 选择一个用户开始对话
4. 发送消息测试实时通信

## 📁 文件结构

### 后端文件
```
celeste-server/src/main/java/com/celeste/
├── config/
│   └── SocketIOConfig.java          # Socket.IO 服务器配置
├── service/
│   └── ChatService.java             # 聊天业务逻辑
├── listener/
│   └── SocketIOEventListener.java   # Socket.IO 事件处理
├── controller/
│   └── MessageController.java       # 消息 REST API
├── repository/
│   └── MessageRepository.java       # 消息数据访问
└── entity/
    └── Message.java                 # 消息实体
```

### 前端文件
```
celeste-web/src/
├── services/
│   └── socketService.js             # Socket.IO 客户端服务
├── stores/model/
│   └── chat.js                      # 聊天状态管理
├── views/
│   ├── ChatView.vue                 # 聊天主界面
│   └── SocketTestView.vue           # Socket.IO 连接测试页面
├── api/
│   └── messageApi.js                # 消息 API 调用
├── components/
│   ├── Chat.vue                     # 聊天消息组件
│   └── Image.vue                    # 图片显示组件
└── public/
    └── default-avatar.svg           # 默认头像
```

## 🔍 故障排除

### 1. Socket.IO 连接失败

检查以下几点：
- 确保后端服务器在端口 9092 上运行
- 检查 JWT token 是否有效
- 查看浏览器控制台的错误信息

### 2. 消息发送失败

可能的原因：
- 数据库连接问题
- Message 表不存在
- 用户未正确认证

### 3. 前端连接问题

检查：
- Socket.IO 客户端版本兼容性
- 网络连接状态
- 浏览器控制台错误

## 📝 开发注意事项

1. **实体修改后重新编译**：修改 Jimmer 实体后必须重新编译项目
2. **JWT Token 传递**：确保前端正确传递 JWT token 作为查询参数
3. **错误处理**：添加适当的错误处理和用户提示
4. **性能优化**：考虑消息分页和连接池优化

## 🎯 下一步开发建议

1. 添加消息状态（已发送、已送达、已读）
2. 实现群聊功能
3. 添加文件和图片发送
4. 实现消息搜索功能
5. 添加消息加密
6. 实现推送通知

## 📞 技术支持

如果遇到问题，请检查：
1. 后端日志输出
2. 前端浏览器控制台
3. 数据库连接状态
4. Socket.IO 服务器状态

系统现在应该可以正常运行实时聊天功能了！🎉
