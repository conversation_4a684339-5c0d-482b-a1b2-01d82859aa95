package com.celeste.entity;

import org.babyfish.jimmer.sql.*;
import org.jetbrains.annotations.Nullable;

@Entity
public interface Message extends Base {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id();

    @ManyToOne
    User sender();

    @IdView("sender")
    long senderId();

    @ManyToOne
    User recipient();

    @IdView("recipient")
    long recipientId();

    @Nullable
    String content();

    @Nullable
    String images();
}
