package com.celeste.repository;

import com.celeste.entity.Message;
import com.celeste.entity.MessageFetcher;
import com.celeste.entity.MessageTable;
import com.celeste.entity.UserFetcher;
import org.babyfish.jimmer.Page;
import org.babyfish.jimmer.spring.repository.JRepository;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public interface MessageRepository extends JRepository<Message, Long> {

    MessageTable table = MessageTable.$;

    /**
     * 获取用户的聊天列表（最近联系人）
     * 使用子查询获取每个对话伙伴的最新消息
     */
    default List<Message> getRecentChatsByUserId(long userId) {
        // 由于Jimmer的窗口函数支持有限，我们使用Java代码进行去重
        List<Message> allMessages = sql()
                .createQuery(table)
                .where(table.senderId().eq(userId).or(table.recipientId().eq(userId)))
                .orderBy(table.createTime().desc())
                .select(table.fetch(
                    MessageFetcher.$
                        .allScalarFields()
                        .senderId()
                        .recipientId()
                        .sender(UserFetcher.$.username())
                        .recipient(UserFetcher.$.username())
                ))
                .execute();

        // Java代码去重：保留每个对话伙伴的最新消息
        Map<Long, Message> uniqueChats = new LinkedHashMap<>();
        for (Message message : allMessages) {
            Long partnerId = message.sender().id() == userId ?
                message.recipient().id() : message.sender().id();

            // 如果这个对话伙伴还没有记录，或者当前消息更新，则保存
            if (!uniqueChats.containsKey(partnerId)) {
                uniqueChats.put(partnerId, message);
            }
        }

        return new ArrayList<>(uniqueChats.values());
    }

    /**
     * 获取用户的聊天列表（最近联系人）- 简化版本
     * 先获取所有消息，然后在应用层去重
     */
    default List<Message> getRecentChatsByUserIdSimple(long userId) {
        // 获取用户相关的所有消息，按时间倒序
        List<Message> allMessages = sql()
                .createQuery(table)
                .where(table.senderId().eq(userId).or(table.recipientId().eq(userId)))
                .orderBy(table.createTime().desc())
                .select(table.fetch(
                    MessageFetcher.$
                        .allScalarFields()
                        .senderId()
                        .recipientId()
                        .sender(UserFetcher.$.username())
                        .recipient(UserFetcher.$.username())
                ))
                .limit(100) // 限制查询数量，提高性能
                .execute();

        // 在应用层去重
        Map<Long, Message> latestMessages = new LinkedHashMap<>();
        for (Message message : allMessages) {
            Long partnerId = message.sender().id() == userId ?
                message.recipient().id() : message.sender().id();

            if (!latestMessages.containsKey(partnerId)) {
                latestMessages.put(partnerId, message);
            }
        }

        return new ArrayList<>(latestMessages.values());
    }

    /**
     * 获取两个用户之间的聊天记录
     */
    default Page<Message> getChatHistoryBetweenUsers(long userId1, long userId2, Pageable pageable) {
        return sql()
                .createQuery(table)
                .where(
                    table.senderId().eq(userId1).and(table.recipientId().eq(userId2))
                    .or(table.senderId().eq(userId2).and(table.recipientId().eq(userId1)))
                )
                .orderBy(table.createTime().asc())
                .select(table.fetch(
                    MessageFetcher.$
                        .allScalarFields()
                        .senderId()
                        .recipientId()
                        .sender(UserFetcher.$.username())
                        .recipient(UserFetcher.$.username())
                ))
                .fetchPage(pageable.getPageNumber(), pageable.getPageSize());
    }
}
