package com.celeste.utils;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.signers.JWTSignerUtil;
import com.celeste.model.SecurityUser;

import java.util.Date;
import java.util.concurrent.TimeUnit;

public class JwtUtil {

    private final static int expire = 3;

    private final static byte[] key = "Cele4TeR".getBytes();

    public static String create(SecurityUser securityUser) {
        return JWT.create()
                .setJWTId(IdUtil.randomUUID())
                .setIssuedAt(new Date())
                .setExpiresAt(DateUtil.offsetDay(new Date(), expire))
                .setPayload("id", Long.toString(securityUser.user().id()))
                .setKey(key)
                .sign();
    }

    public static JWT parse(String token) {
        if (token == null || !token.startsWith("Bearer ")) return null;
        JWT jwt = JWT.of(token.substring(7));

        // 检查JWT黑名单（如果Redis可用）
        try {
            if (RedisUtil.hasKey(RedisUtil.JWT_BLACKLIST + jwt.getPayload("jti"))) {
                return null;
            }
        } catch (Exception e) {
            // Redis不可用时，跳过黑名单检查，继续验证JWT
            System.out.println("Redis不可用，跳过JWT黑名单检查: " + e.getMessage());
        }

        if (!jwt.setSigner(JWTSignerUtil.hs256(key)).validate(0)) return null;
        return jwt;
    }

    public static void addBlacklist(String token) {
        JWT jwt = parse(token);
        if (jwt != null) {
            try {
                long expire = DateUtil.betweenMs(Convert.toDate(jwt.getPayload("exp")), new Date());
                RedisUtil.useString(RedisUtil.JWT_BLACKLIST + jwt.getPayload("jti"), token.substring(7), expire, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                // Redis不可用时，无法添加到黑名单，但不影响其他功能
                System.out.println("Redis不可用，无法添加JWT到黑名单: " + e.getMessage());
            }
        }
    }

}
