package com.celeste.utils;

import cn.hutool.log.StaticLog;
import com.celeste.Application;
import com.feiniaojin.gracefulresponse.GracefulResponseException;
import io.minio.*;
import io.minio.http.Method;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.io.IOUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.concurrent.TimeUnit;

public class MinioUtil {

    private final static String bucketName = "celeste";
    private static MinioClient client;

    private static MinioClient getClient() {
        if (client == null) {
            try {
                if (Application.applicationContext != null) {
                    client = Application.applicationContext.getBean(MinioClient.class);
                }
            } catch (Exception e) {
                StaticLog.error("MinioClient初始化失败: {}", e.getMessage());
                return null;
            }
        }
        return client;
    }

    public static void getAvatar(String username, HttpServletResponse response) {
        String objectName = "avatar/" + username + ".jpg";
        MinioClient minioClient = getClient();

        // 设置响应头
        response.setContentType("image/jpeg");
        response.setHeader("Cache-Control", "public, max-age=3600"); // 缓存1小时

        if (minioClient == null) {
            StaticLog.error("MinioClient不可用，无法获取头像: {}", objectName);
            sendDefaultAvatar(response);
            return;
        }

        try {
            GetObjectResponse object = minioClient.getObject(GetObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build());
            IOUtils.copyLarge(object, response.getOutputStream());
            StaticLog.debug("头像获取成功: {}", objectName);
        } catch (Exception e) {
            StaticLog.info("头像获取失败，返回默认头像: {}", objectName);
            sendDefaultAvatar(response);
        }
    }

    private static void sendDefaultAvatar(HttpServletResponse response) {
        try {
            // 生成一个简单的默认头像SVG
            String defaultAvatarSvg = """
                <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="32" cy="32" r="32" fill="#F3F4F6"/>
                    <circle cx="32" cy="24" r="10" fill="#9CA3AF"/>
                    <path d="M12 52C12 43.1634 19.1634 36 28 36H36C44.8366 36 52 43.1634 52 52V64H12V52Z" fill="#9CA3AF"/>
                </svg>
                """;

            response.setContentType("image/svg+xml");
            response.getWriter().write(defaultAvatarSvg);
            response.getWriter().flush();
        } catch (Exception ex) {
            StaticLog.error("发送默认头像失败: {}", ex.getMessage());
        }
    }

    public static void uploadFile(MultipartFile file, String objectName) {
        if (file.isEmpty()) throw new GracefulResponseException("文件大小至少为1字节");
        MinioClient minioClient = getClient();
        if (minioClient == null) {
            StaticLog.error("MinioClient不可用，无法上传文件: {}", objectName);
            return;
        }

        try {
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .build());
        } catch (Exception e) {
            StaticLog.info("文件上传失败:{}", objectName);
        }
    }

    public static String getUrl(String objectName) {
        String url = "";
        MinioClient minioClient = getClient();
        if (minioClient == null) {
            StaticLog.error("MinioClient不可用，无法获取URL: {}", objectName);
            return url;
        }

        try {
            url = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(bucketName)
                    .object(objectName)
                    .expiry(1, TimeUnit.DAYS)
                    .build());
        } catch (Exception e) {
            StaticLog.info("Url获取失败:{}", objectName);
        }
        return url;
    }

}
