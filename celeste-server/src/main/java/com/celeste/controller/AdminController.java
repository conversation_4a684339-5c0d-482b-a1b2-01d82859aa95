package com.celeste.controller;

import com.celeste.entity.*;
import com.celeste.repository.UserRepository;
import com.celeste.repository.VideoRepository;
import com.feiniaojin.gracefulresponse.GracefulResponseException;
import jakarta.annotation.Resource;
import org.babyfish.jimmer.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/admin")
// @PreAuthorize("hasRole('ADMIN')") // 临时注释掉用于调试
public class AdminController {

    @Resource
    private UserRepository userRepository;

    @Resource
    private VideoRepository videoRepository;

    // 用户管理相关API

    @GetMapping("/users")
    public Page<User> getAllUsers(@RequestParam(defaultValue = "0") int page,
                                  @RequestParam(defaultValue = "20") int size) {
        return userRepository.findAllUsers(page, size);
    }

    @GetMapping("/users/search")
    public List<User> searchUsers(@RequestParam String keyword) {
        return userRepository.searchUsers(keyword);
    }

    @PostMapping("/users/{userId}/disable")
    public void disableUser(@PathVariable long userId) {
        if (userRepository.updateUserStatus(userId, "DISABLED") == 0) {
            throw new GracefulResponseException("用户禁用失败，请联系管理员");
        }
    }

    @PostMapping("/users/{userId}/enable")
    public void enableUser(@PathVariable long userId) {
        if (userRepository.updateUserStatus(userId, "ACTIVE") == 0) {
            throw new GracefulResponseException("用户启用失败，请联系管理员");
        }
    }

    // 视频管理相关API

    @GetMapping("/videos")
    public Page<Video> getAllVideos(@RequestParam(defaultValue = "0") int page,
                                    @RequestParam(defaultValue = "20") int size) {
        return videoRepository.findAllVideos(page, size);
    }

    @GetMapping("/videos/search")
    public List<Video> searchVideos(@RequestParam String keyword) {
        return videoRepository.searchVideoForAdmin(keyword);
    }

    @PostMapping("/videos/{videoId}/remove")
    public void removeVideo(@PathVariable long videoId) {
        if (videoRepository.updateVideoStatus(videoId, "REMOVED") == 0) {
            throw new GracefulResponseException("视频下架失败，请联系管理员");
        }
    }

    @PostMapping("/videos/{videoId}/restore")
    public void restoreVideo(@PathVariable long videoId) {
        if (videoRepository.updateVideoStatus(videoId, "ACTIVE") == 0) {
            throw new GracefulResponseException("视频恢复失败，请联系管理员");
        }
    }

    // 统计信息API

    @GetMapping("/statistics")
    public AdminStatistics getStatistics() {
        long totalUsers = userRepository.countUsers();
        long activeUsers = userRepository.countUsersByStatus("ACTIVE");
        long disabledUsers = userRepository.countUsersByStatus("DISABLED");

        long totalVideos = videoRepository.countVideos();
        long activeVideos = videoRepository.countVideosByStatus("ACTIVE");
        long removedVideos = videoRepository.countVideosByStatus("REMOVED");

        return new AdminStatistics(
            totalUsers, activeUsers, disabledUsers,
            totalVideos, activeVideos, removedVideos
        );
    }

    // 统计信息数据类
    public record AdminStatistics(
        long totalUsers,
        long activeUsers,
        long disabledUsers,
        long totalVideos,
        long activeVideos,
        long removedVideos
    ) {}
}
