package com.celeste.controller;

import com.celeste.model.SecurityUser;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/auth-test")
public class AuthTestController {

    @GetMapping("/current-user")
    public Map<String, Object> getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Map<String, Object> result = new HashMap<>();

        result.put("authenticationExists", authentication != null);

        if (authentication != null) {
            result.put("isAuthenticated", authentication.isAuthenticated());
            result.put("authenticationClass", authentication.getClass().getSimpleName());

            Object principal = authentication.getPrincipal();
            result.put("principalType", principal.getClass().getSimpleName());

            if (principal instanceof SecurityUser securityUser) {
                result.put("username", securityUser.getUsername());
                result.put("userId", securityUser.user().id());
                result.put("userRoles", securityUser.user().roleList());

                Collection<? extends GrantedAuthority> authorities = securityUser.getAuthorities();
                result.put("springAuthorities", authorities.stream()
                        .map(GrantedAuthority::getAuthority)
                        .toList());

                // 检查是否有ADMIN权限
                boolean hasAdminRole = authorities.stream()
                        .anyMatch(auth -> "ROLE_ADMIN".equals(auth.getAuthority()));
                result.put("hasAdminRole", hasAdminRole);

            } else if (principal instanceof String) {
                result.put("principalString", principal);
            } else {
                result.put("principal", principal.toString());
            }

            // 检查所有权限
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
            result.put("allAuthorities", authorities.stream()
                    .map(GrantedAuthority::getAuthority)
                    .toList());
        } else {
            result.put("message", "No authentication found");
        }

        return result;
    }

    @GetMapping("/admin-test")
    public Map<String, Object> adminTest() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "如果你能看到这个消息，说明你有管理员权限");
        result.put("timestamp", System.currentTimeMillis());

        // 同时检查当前用户权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof SecurityUser securityUser) {
            result.put("currentUser", securityUser.getUsername());
            result.put("hasAdminRole", securityUser.getAuthorities().stream()
                    .anyMatch(auth -> "ROLE_ADMIN".equals(auth.getAuthority())));
        }

        return result;
    }

    @GetMapping("/admin-test-no-auth")
    public Map<String, Object> adminTestNoAuth() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "这个接口不需要权限验证，任何人都可以访问");
        result.put("timestamp", System.currentTimeMillis());

        // 检查当前用户权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null) {
            result.put("authenticated", authentication.isAuthenticated());
            if (authentication.getPrincipal() instanceof SecurityUser securityUser) {
                result.put("currentUser", securityUser.getUsername());
                result.put("authorities", securityUser.getAuthorities().stream()
                        .map(GrantedAuthority::getAuthority)
                        .toList());
            }
        } else {
            result.put("authenticated", false);
        }

        return result;
    }

    @GetMapping("/check-user/{username}")
    public Map<String, Object> checkUser(@PathVariable String username) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 这里需要注入UserRepository
            result.put("message", "需要在控制器中注入UserRepository来查询用户");
            result.put("username", username);
        } catch (Exception e) {
            result.put("error", e.getMessage());
        }
        return result;
    }
}
