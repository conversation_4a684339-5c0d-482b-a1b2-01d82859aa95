package com.celeste.controller;

import com.celeste.entity.Collection;
import com.celeste.entity.Video;
import com.celeste.model.SecurityUser;
import com.celeste.repository.CollectionRepository;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/collection")
public class CollectionController {

    @Resource
    private CollectionRepository collectionRepository;

    @GetMapping("/createCollection")
    @PreAuthorize("isAuthenticated()")
    public Collection createCollection(Authentication authentication, String name) {
        SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
        return collectionRepository.insertCollection(securityUser.user().id(), name);
    }

    @GetMapping("/getMyCollections")
    @PreAuthorize("isAuthenticated()")
    public List<Collection> getMyCollections(Authentication authentication) {
        SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
        return collectionRepository.listCollectionsByUserId(securityUser.user().id());
    }

    @GetMapping("/getCollectedVideos")
    @PreAuthorize("isAuthenticated()")
    public List<Video> getCollectedVideos(Authentication authentication) {
        SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
        return collectionRepository.listCollectedVideosByUserId(securityUser.user().id());
    }

    @GetMapping("/getCollectedVideos/{userId}")
    public List<Video> getCollectedVideosByUserId(@PathVariable long userId) {
        return collectionRepository.listCollectedVideosByUserId(userId);
    }

    @GetMapping("/getCollectionVideos/{collectionId}")
    public List<Video> getCollectionVideos(@PathVariable long collectionId) {
        return collectionRepository.listVideosByCollectionId(collectionId);
    }

    @GetMapping("/getUserCollections/{userId}")
    public List<Collection> getUserCollections(@PathVariable long userId) {
        return collectionRepository.listCollectionsByUserId(userId);
    }
}
