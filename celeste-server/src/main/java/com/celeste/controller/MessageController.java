package com.celeste.controller;

import com.celeste.entity.Message;
import com.celeste.entity.MessageDraft;
import com.celeste.entity.MessageFetcher;
import com.celeste.entity.MessageTable;
import com.celeste.entity.UserFetcher;
import com.celeste.model.SecurityUser;
import com.celeste.repository.MessageRepository;
import com.celeste.service.ChatService;
import jakarta.annotation.Resource;
import org.babyfish.jimmer.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/message")
public class MessageController {

    @Resource
    private MessageRepository messageRepository;

    @Resource
    private ChatService chatService;

    /**
     * 获取用户的聊天列表（最近联系人）
     */
    @GetMapping("/recent")
    @PreAuthorize("isAuthenticated()")
    public List<Message> getRecentChats(Authentication authentication) {
        SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
        return messageRepository.getRecentChatsByUserId(securityUser.user().id());
    }

    /**
     * 获取与指定用户的聊天记录
     */
    @GetMapping("/history/{otherUserId}")
    @PreAuthorize("isAuthenticated()")
    public Page<Message> getChatHistory(
            @PathVariable long otherUserId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {
        SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
        Pageable pageable = PageRequest.of(page, size);
        return messageRepository.getChatHistoryBetweenUsers(securityUser.user().id(), otherUserId, pageable);
    }

    /**
     * 获取用户列表（用于选择聊天对象）
     */
    @GetMapping("/users/{userId}")
    public List<Message> getUserList(@PathVariable long userId) {
        return messageRepository.getRecentChatsByUserId(userId);
    }

    /**
     * 测试端点 - 检查聊天系统状态
     */
    @GetMapping("/status")
    public String getStatus() {
        return "Chat system is running";
    }

    /**
     * 调试端点 - 获取原始聊天数据（未去重）
     */
    @GetMapping("/debug/raw/{userId}")
    public List<Message> getRawChatData(@PathVariable long userId) {
        MessageTable table = MessageTable.$;
        return messageRepository.sql()
                .createQuery(table)
                .where(table.senderId().eq(userId)
                    .or(table.recipientId().eq(userId)))
                .orderBy(table.createTime().desc())
                .select(table.fetch(
                    MessageFetcher.$
                        .allScalarFields()
                        .senderId()
                        .recipientId()
                        .sender(UserFetcher.$.username())
                        .recipient(UserFetcher.$.username())
                ))
                .execute();
    }

    /**
     * 调试端点 - 测试去重后的聊天列表
     */
    @GetMapping("/debug/unique/{userId}")
    public List<Message> getUniqueChatData(@PathVariable long userId) {
        return messageRepository.getRecentChatsByUserId(userId);
    }

    /**
     * 调试端点 - 测试简化版去重
     */
    @GetMapping("/debug/simple/{userId}")
    public List<Message> getSimpleChatData(@PathVariable long userId) {
        return messageRepository.getRecentChatsByUserIdSimple(userId);
    }

    /**
     * 调试端点 - 测试发送消息
     */
    @PostMapping("/debug/send")
    public String testSendMessage(
            @RequestParam long senderId,
            @RequestParam long recipientId,
            @RequestParam String content,
            @RequestParam(required = false) String images) {
        try {
            Message message = chatService.sendMessage(senderId, recipientId, content, images);
            return "消息发送成功，ID: " + message.id() +
                   ", content: " + message.content() +
                   ", images: " + (message.images() != null ? message.images() : "null") +
                   ", createTime: " + message.createTime();
        } catch (Exception e) {
            e.printStackTrace();
            return "发送失败: " + e.getMessage();
        }
    }

    /**
     * 调试端点 - 简单测试（不验证用户）
     */
    @PostMapping("/debug/simple-send")
    public String testSimpleSend(@RequestParam String content) {
        try {
            // 直接使用MessageDraft创建消息，不验证用户
            Message message = MessageDraft.$.produce(draft -> {
                draft.setSenderId(1L);
                draft.setRecipientId(2L);
                draft.setContent(content);
                draft.setImages(null);
                draft.setCreateTime(new java.util.Date());
            });

            Message saved = messageRepository.save(message);
            return "简单消息发送成功，ID: " + saved.id();
        } catch (Exception e) {
            e.printStackTrace();
            return "简单发送失败: " + e.getMessage();
        }
    }
}
