package com.celeste.service;

import com.celeste.entity.*;
import com.celeste.repository.MessageRepository;
import com.celeste.repository.UserRepository;
import jakarta.annotation.Resource;
import org.babyfish.jimmer.Page;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ChatService {

    @Resource
    private MessageRepository messageRepository;

    @Resource
    private UserRepository userRepository;

    MessageTable table = MessageTable.$;

    /**
     * 发送消息
     */
    public Message sendMessage(long senderId, long recipientId, String content, String images) {
        try {
            System.out.println("开始发送消息: " + senderId + " -> " + recipientId + ", content: " + content + ", images: " + images);

            // 获取用户对象（同时验证用户是否存在）
            User senderUser = userRepository.findById(senderId).orElseThrow(() ->
                new RuntimeException("发送者用户不存在: " + senderId));
            User recipientUser = userRepository.findById(recipientId).orElseThrow(() ->
                new RuntimeException("接收者用户不存在: " + recipientId));

            Message message = MessageDraft.$.produce(draft -> {
                // 使用关联对象而不是ID
                draft.setSender(senderUser);
                draft.setRecipient(recipientUser);
                draft.setContent(content);
                // Always set images, even if null - this prevents "unloaded" property error
                draft.setImages(images);
                draft.setCreateTime(new java.util.Date());

                // 调试信息
                System.out.println("🔍 MessageDraft 调试:");
                System.out.println("- 设置 sender: " + senderUser.id() + " (" + senderUser.username() + ")");
                System.out.println("- 设置 recipient: " + recipientUser.id() + " (" + recipientUser.username() + ")");
            });

            Message savedMessage = messageRepository.save(message);
            System.out.println("消息已保存，ID: " + savedMessage.id());
            System.out.println("🔍 保存后的消息:");
            System.out.println("- savedMessage.senderId(): " + savedMessage.senderId());
            System.out.println("- savedMessage.recipientId(): " + savedMessage.recipientId());

            // Fetch the complete message with all properties to avoid "unloaded" errors
            Message completeMessage = messageRepository.sql()
                    .createQuery(table)
                    .where(table.id().eq(savedMessage.id()))
                    .select(table.fetch(
                        MessageFetcher.$
                            .allScalarFields()
                            .senderId()
                            .recipientId()
                            .sender(UserFetcher.$.username())
                            .recipient(UserFetcher.$.username())
                    ))
                    .fetchOne();

            System.out.println("完整消息已获取，images: " + completeMessage.images());
            return completeMessage;
        } catch (Exception e) {
            throw new RuntimeException("发送消息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取两个用户之间的聊天记录
     */
    public Page<Message> getChatHistory(long userId1, long userId2, int page, int size) {
        return messageRepository.sql()
                .createQuery(table)
                .where(
                    table.senderId().eq(userId1).and(table.recipientId().eq(userId2))
                    .or(table.senderId().eq(userId2).and(table.recipientId().eq(userId1)))
                )
                .orderBy(table.createTime().asc())
                .select(table.fetch(
                    MessageFetcher.$
                        .allScalarFields()
                        .senderId()
                        .recipientId()
                        .sender(UserFetcher.$.username())
                        .recipient(UserFetcher.$.username())
                ))
                .fetchPage(page, size);
    }

    /**
     * 获取用户的聊天列表（最近联系人）
     */
    public List<Message> getRecentChats(long userId) {
        return messageRepository.getRecentChatsByUserId(userId);
    }

    /**
     * 标记消息为已读
     */
    public void markAsRead(long messageId) {
        // 这里可以添加已读状态的逻辑
        // 如果需要已读功能，可以在Message实体中添加isRead字段
    }
}
