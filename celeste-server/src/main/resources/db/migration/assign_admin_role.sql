-- 为现有用户分配管理员角色的脚本
-- 请根据需要修改用户名

-- 方法1：为特定用户名分配管理员角色
-- 请将 'your_username' 替换为实际的用户名
UPDATE user_role_mapping 
SET role_id = 2 
WHERE user_id = (SELECT id FROM user WHERE username = 'your_username' LIMIT 1);

-- 如果用户还没有角色记录，则插入管理员角色
INSERT IGNORE INTO user_role_mapping (user_id, role_id)
SELECT id, 2 FROM user WHERE username = 'your_username';

-- 方法2：为用户ID分配管理员角色
-- 请将 1 替换为实际的用户ID
-- INSERT IGNORE INTO user_role_mapping (user_id, role_id) VALUES (1, 2);

-- 验证角色分配
SELECT u.id, u.username, u.email, r.name as role_name
FROM user u
JOIN user_role_mapping urm ON u.id = urm.user_id
JOIN role r ON urm.role_id = r.id
WHERE r.name = 'ADMIN';

-- 查看所有用户的角色
SELECT u.id, u.username, u.email, GROUP_CONCAT(r.name) as roles
FROM user u
LEFT JOIN user_role_mapping urm ON u.id = urm.user_id
LEFT JOIN role r ON urm.role_id = r.id
GROUP BY u.id, u.username, u.email
ORDER BY u.id;
