-- 创建管理员用户脚本
-- 用户名: admin
-- 密码: admin123

-- 首先确保角色表存在数据
INSERT IGNORE INTO role (id, name) VALUES (1, 'USER'), (2, 'ADMIN');

-- 删除可能存在的admin用户（避免冲突）
SET @admin_user_id = (SELECT id FROM user WHERE username = 'admin');
DELETE FROM user_role_mapping WHERE user_id = @admin_user_id;
DELETE FROM user WHERE username = 'admin';

-- 创建管理员用户
-- 使用BCrypt加密的密码 'admin123'
INSERT INTO user (username, password, email, gender, home, status, create_time) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjKUiFjFO2/fG', '<EMAIL>', '男', '中国', 'ACTIVE', NOW());

-- 获取新创建的admin用户ID
SET @new_admin_id = LAST_INSERT_ID();

-- 为管理员分配ADMIN角色
INSERT INTO user_role_mapping (user_id, role_id) VALUES (@new_admin_id, 2);

-- 验证创建结果
SELECT
    u.id,
    u.username,
    u.email,
    u.status,
    GROUP_CONCAT(r.name) as roles
FROM user u
LEFT JOIN user_role_mapping urm ON u.id = urm.user_id
LEFT JOIN role r ON urm.role_id = r.id
WHERE u.username = 'admin'
GROUP BY u.id, u.username, u.email, u.status;

-- 显示所有用户的角色（用于调试）
SELECT
    u.id,
    u.username,
    u.email,
    GROUP_CONCAT(r.name) as roles
FROM user u
LEFT JOIN user_role_mapping urm ON u.id = urm.user_id
LEFT JOIN role r ON urm.role_id = r.id
GROUP BY u.id, u.username, u.email
ORDER BY u.id;

SELECT 'Admin user created successfully!' as message;
