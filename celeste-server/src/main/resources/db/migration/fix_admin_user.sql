-- 修复admin用户创建问题
-- 解决字段长度问题

-- 1. 确保角色表存在
INSERT IGNORE INTO role (id, name) VALUES (1, 'USER'), (2, 'ADMIN');

-- 2. 删除可能存在的admin用户
DELETE FROM user_role_mapping WHERE user_id IN (SELECT id FROM user WHERE username = 'admin');
DELETE FROM user WHERE username = 'admin';

-- 3. 创建admin用户（使用较短的字段值）
INSERT INTO user (username, password, email, gender, home, status, create_time) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjKUiFjFO2/fG', '<EMAIL>', '男', '隐藏', 'ACTIVE', NOW());

-- 4. 获取admin用户ID并分配ADMIN角色
SET @admin_id = (SELECT id FROM user WHERE username = 'admin');
INSERT INTO user_role_mapping (user_id, role_id) VALUES (@admin_id, 2);

-- 5. 验证创建结果
SELECT 
    u.id,
    u.username,
    u.email,
    u.gender,
    u.home,
    u.status,
    GROUP_CONCAT(r.name) as roles
FROM user u
LEFT JOIN user_role_mapping urm ON u.id = urm.user_id
LEFT JOIN role r ON urm.role_id = r.id
WHERE u.username = 'admin'
GROUP BY u.id, u.username, u.email, u.gender, u.home, u.status;

-- 6. 显示成功消息
SELECT 'Admin user created successfully! Username: admin, Password: admin123' as message;
