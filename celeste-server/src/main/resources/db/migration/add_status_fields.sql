-- 添加用户和视频状态字段
-- 请在MySQL数据库中执行此脚本



-- 更新现有数据的状态为ACTIVE
UPDATE user SET status = 'ACTIVE' WHERE status IS NULL;
UPDATE video SET status = 'ACTIVE' WHERE status IS NULL;

-- 创建管理员角色和权限（如果不存在）
INSERT IGNORE INTO role (id, name) VALUES (1, 'USER'), (2, 'ADMIN');
INSERT IGNORE INTO authority (id, name) VALUES (1, 'READ'), (2, 'write'), (3, 'admin');

-- 创建角色权限关联
INSERT IGNORE INTO role_authority_mapping (role_id, authority_id) VALUES
(1, 1), -- USER角色有读权限
(2, 1), -- ADMIN角色有读权限
(2, 2), -- ADMIN角色有写权限
(2, 3); -- ADMIN角色有管理员权限

-- 为现有用户分配USER角色（如果没有角色）
INSERT IGNORE INTO user_role_mapping (user_id, role_id)
SELECT u.id, 1 FROM user u
LEFT JOIN user_role_mapping urm ON u.id = urm.user_id
WHERE urm.user_id IS NULL;

-- 检查并创建必要的表（如果不存在）
CREATE TABLE IF NOT EXISTS role (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE
);

CREATE TABLE IF NOT EXISTS authority (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE
);

CREATE TABLE IF NOT EXISTS user_role_mapping (
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES role(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS role_authority_mapping (
    role_id BIGINT NOT NULL,
    authority_id BIGINT NOT NULL,
    PRIMARY KEY (role_id, authority_id),
    FOREIGN KEY (role_id) REFERENCES role(id) ON DELETE CASCADE,
    FOREIGN KEY (authority_id) REFERENCES authority(id) ON DELETE CASCADE
);

-- 删除可能存在的admin用户（重新创建）
DELETE FROM user_role_mapping WHERE user_id = (SELECT id FROM user WHERE username = 'admin');
DELETE FROM user WHERE username = 'admin';

-- 创建管理员用户
-- 密码是 'admin123' 的BCrypt哈希值
INSERT INTO user (username, password, email, gender, home, status, create_time) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjKUiFjFO2/fG', '<EMAIL>', '男', '中国', 'ACTIVE', NOW());

-- 为管理员用户分配ADMIN角色
INSERT INTO user_role_mapping (user_id, role_id)
SELECT id, 2 FROM user WHERE username = 'admin';

-- 验证数据
SELECT 'Status fields added successfully' as result;
SELECT COUNT(*) as user_count FROM user WHERE status = 'ACTIVE';
SELECT COUNT(*) as video_count FROM video WHERE status = 'ACTIVE';
SELECT u.username, r.name as role FROM user u
JOIN user_role_mapping urm ON u.id = urm.user_id
JOIN role r ON urm.role_id = r.id
WHERE u.id = 999;
