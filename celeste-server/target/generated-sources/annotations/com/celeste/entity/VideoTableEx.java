package com.celeste.entity;

import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.function.Function;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.sql.JoinType;
import org.babyfish.jimmer.sql.ast.Predicate;
import org.babyfish.jimmer.sql.ast.impl.table.TableImplementor;
import org.babyfish.jimmer.sql.ast.impl.table.TableProxies;
import org.babyfish.jimmer.sql.ast.table.Table;
import org.babyfish.jimmer.sql.ast.table.TableEx;
import org.babyfish.jimmer.sql.ast.table.WeakJoin;
import org.babyfish.jimmer.sql.ast.table.spi.AbstractTypedTable;

@GeneratedBy(
        type = Video.class
)
public class VideoTableEx extends VideoTable implements TableEx<Video> {
    public static final VideoTableEx $ = new VideoTableEx(VideoTable.$, null);

    public VideoTableEx() {
        super();
    }

    public VideoTableEx(AbstractTypedTable.DelayedOperation<Video> delayedOperation) {
        super(delayedOperation);
    }

    public VideoTableEx(TableImplementor<Video> table) {
        super(table);
    }

    protected VideoTableEx(VideoTable base, String joinDisabledReason) {
        super(base, joinDisabledReason);
    }

    public TypeTableEx type() {
        __beforeJoin();
        if (raw != null) {
            return new TypeTableEx(raw.joinImplementor(VideoProps.TYPE.unwrap()));
        }
        return new TypeTableEx(joinOperation(VideoProps.TYPE.unwrap()));
    }

    public TypeTableEx type(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new TypeTableEx(raw.joinImplementor(VideoProps.TYPE.unwrap(), joinType));
        }
        return new TypeTableEx(joinOperation(VideoProps.TYPE.unwrap(), joinType));
    }

    public UserTableEx user() {
        __beforeJoin();
        if (raw != null) {
            return new UserTableEx(raw.joinImplementor(VideoProps.USER.unwrap()));
        }
        return new UserTableEx(joinOperation(VideoProps.USER.unwrap()));
    }

    public UserTableEx user(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new UserTableEx(raw.joinImplementor(VideoProps.USER.unwrap(), joinType));
        }
        return new UserTableEx(joinOperation(VideoProps.USER.unwrap(), joinType));
    }

    public VideoLikeTableEx likes() {
        __beforeJoin();
        if (raw != null) {
            return new VideoLikeTableEx(raw.joinImplementor(VideoProps.LIKES.unwrap()));
        }
        return new VideoLikeTableEx(joinOperation(VideoProps.LIKES.unwrap()));
    }

    public VideoLikeTableEx likes(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new VideoLikeTableEx(raw.joinImplementor(VideoProps.LIKES.unwrap(), joinType));
        }
        return new VideoLikeTableEx(joinOperation(VideoProps.LIKES.unwrap(), joinType));
    }

    @Override
    public Predicate likes(Function<VideoLikeTableEx, Predicate> block) {
        return exists(VideoProps.LIKES.unwrap(), block);
    }

    public CommentTableEx comments() {
        __beforeJoin();
        if (raw != null) {
            return new CommentTableEx(raw.joinImplementor(VideoProps.COMMENTS.unwrap()));
        }
        return new CommentTableEx(joinOperation(VideoProps.COMMENTS.unwrap()));
    }

    public CommentTableEx comments(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new CommentTableEx(raw.joinImplementor(VideoProps.COMMENTS.unwrap(), joinType));
        }
        return new CommentTableEx(joinOperation(VideoProps.COMMENTS.unwrap(), joinType));
    }

    @Override
    public Predicate comments(Function<CommentTableEx, Predicate> block) {
        return exists(VideoProps.COMMENTS.unwrap(), block);
    }

    public CollectionTableEx collections() {
        __beforeJoin();
        if (raw != null) {
            return new CollectionTableEx(raw.joinImplementor(VideoProps.COLLECTIONS.unwrap()));
        }
        return new CollectionTableEx(joinOperation(VideoProps.COLLECTIONS.unwrap()));
    }

    public CollectionTableEx collections(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new CollectionTableEx(raw.joinImplementor(VideoProps.COLLECTIONS.unwrap(), joinType));
        }
        return new CollectionTableEx(joinOperation(VideoProps.COLLECTIONS.unwrap(), joinType));
    }

    @Override
    public Predicate collections(Function<CollectionTableEx, Predicate> block) {
        return exists(VideoProps.COLLECTIONS.unwrap(), block);
    }

    @Override
    public VideoTableEx asTableEx() {
        return this;
    }

    @Override
    public VideoTableEx __disableJoin(String reason) {
        return new VideoTableEx(this, reason);
    }

    public <TT extends Table<?>, WJ extends WeakJoin<VideoTable, TT>> TT weakJoin(
            Class<WJ> weakJoinType) {
        return weakJoin(weakJoinType, JoinType.INNER);
    }

    @SuppressWarnings("unchecked")
    public <TT extends Table<?>, WJ extends WeakJoin<VideoTable, TT>> TT weakJoin(
            Class<WJ> weakJoinType, JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return (TT)TableProxies.wrap(raw.weakJoinImplementor(weakJoinType, joinType));
        }
        return (TT)TableProxies.fluent(joinOperation(weakJoinType, joinType));
    }
}
