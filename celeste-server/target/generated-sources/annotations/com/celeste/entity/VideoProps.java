package com.celeste.entity;

import java.lang.Boolean;
import java.lang.Long;
import java.lang.String;
import java.util.Date;
import java.util.function.Function;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.meta.ImmutableType;
import org.babyfish.jimmer.meta.TypedProp;
import org.babyfish.jimmer.sql.JoinType;
import org.babyfish.jimmer.sql.ast.Predicate;
import org.babyfish.jimmer.sql.ast.PropExpression;
import org.babyfish.jimmer.sql.ast.table.PropsFor;

@GeneratedBy(
        type = Video.class
)
@PropsFor(Video.class)
public interface VideoProps extends BaseProps {
    TypedProp.Scalar<Video, Long> ID = 
        TypedProp.scalar(ImmutableType.get(Video.class).getProp("id"));

    TypedProp.Scalar<Video, Date> CREATE_TIME = 
        TypedProp.scalar(ImmutableType.get(Video.class).getProp("createTime"));

    TypedProp.Scalar<Video, Date> MODIFY_TIME = 
        TypedProp.scalar(ImmutableType.get(Video.class).getProp("modifyTime"));

    TypedProp.Scalar<Video, String> BETWEEN_TIME = 
        TypedProp.scalar(ImmutableType.get(Video.class).getProp("betweenTime"));

    TypedProp.Scalar<Video, String> DESCRIPTION = 
        TypedProp.scalar(ImmutableType.get(Video.class).getProp("description"));

    TypedProp.Scalar<Video, String> STATUS = 
        TypedProp.scalar(ImmutableType.get(Video.class).getProp("status"));

    TypedProp.Reference<Video, Type> TYPE = 
        TypedProp.reference(ImmutableType.get(Video.class).getProp("type"));

    TypedProp.Reference<Video, User> USER = 
        TypedProp.reference(ImmutableType.get(Video.class).getProp("user"));

    TypedProp.ReferenceList<Video, VideoLike> LIKES = 
        TypedProp.referenceList(ImmutableType.get(Video.class).getProp("likes"));

    TypedProp.ReferenceList<Video, Comment> COMMENTS = 
        TypedProp.referenceList(ImmutableType.get(Video.class).getProp("comments"));

    TypedProp.ReferenceList<Video, Collection> COLLECTIONS = 
        TypedProp.referenceList(ImmutableType.get(Video.class).getProp("collections"));

    TypedProp.Scalar<Video, String> SOURCE = 
        TypedProp.scalar(ImmutableType.get(Video.class).getProp("source"));

    TypedProp.Scalar<Video, String> POSTER = 
        TypedProp.scalar(ImmutableType.get(Video.class).getProp("poster"));

    TypedProp.Scalar<Video, Long> LIKES_SUM = 
        TypedProp.scalar(ImmutableType.get(Video.class).getProp("likesSum"));

    TypedProp.Scalar<Video, Long> COMMENTS_SUM = 
        TypedProp.scalar(ImmutableType.get(Video.class).getProp("commentsSum"));

    TypedProp.Scalar<Video, Long> COLLECTIONS_SUM = 
        TypedProp.scalar(ImmutableType.get(Video.class).getProp("collectionsSum"));

    TypedProp.Scalar<Video, Boolean> USER_IS_ADORE = 
        TypedProp.scalar(ImmutableType.get(Video.class).getProp("userIsAdore"));

    TypedProp.Scalar<Video, Boolean> USER_IS_LIKE = 
        TypedProp.scalar(ImmutableType.get(Video.class).getProp("userIsLike"));

    TypedProp.Scalar<Video, Boolean> USER_IS_COLLECT = 
        TypedProp.scalar(ImmutableType.get(Video.class).getProp("userIsCollect"));

    PropExpression.Num<Long> id();

    PropExpression.Str description();

    PropExpression.Str status();

    TypeTable type();

    TypeTable type(JoinType joinType);

    PropExpression.Num<Long> typeId();

    UserTable user();

    UserTable user(JoinType joinType);

    PropExpression.Num<Long> userId();

    Predicate likes(Function<VideoLikeTableEx, Predicate> block);

    Predicate comments(Function<CommentTableEx, Predicate> block);

    Predicate collections(Function<CollectionTableEx, Predicate> block);
}
