package com.celeste.entity;

import java.lang.Deprecated;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import java.util.Date;
import java.util.function.Function;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.sql.ast.Predicate;
import org.babyfish.jimmer.sql.ast.PropExpression;
import org.babyfish.jimmer.sql.ast.impl.table.TableImplementor;
import org.babyfish.jimmer.sql.ast.table.TableEx;
import org.babyfish.jimmer.sql.ast.table.spi.AbstractTypedTable;

@GeneratedBy(
        type = User.class
)
public class UserTable extends AbstractTypedTable<User> implements UserProps {
    public static final UserTable $ = new UserTable();

    public UserTable() {
        super(User.class);
    }

    public UserTable(AbstractTypedTable.DelayedOperation<User> delayedOperation) {
        super(User.class, delayedOperation);
    }

    public UserTable(TableImplementor<User> table) {
        super(table);
    }

    protected UserTable(UserTable base, String joinDisabledReason) {
        super(base, joinDisabledReason);
    }

    @Override
    public PropExpression.Num<Long> id() {
        return __get(UserProps.ID.unwrap());
    }

    @Override
    public PropExpression.Cmp<Date> createTime() {
        return __get(UserProps.CREATE_TIME.unwrap());
    }

    @Override
    public PropExpression.Cmp<Date> modifyTime() {
        return __get(UserProps.MODIFY_TIME.unwrap());
    }

    @Override
    public PropExpression.Str username() {
        return __get(UserProps.USERNAME.unwrap());
    }

    @Override
    public PropExpression.Str password() {
        return __get(UserProps.PASSWORD.unwrap());
    }

    @Override
    public PropExpression.Str email() {
        return __get(UserProps.EMAIL.unwrap());
    }

    @Override
    public PropExpression.Str description() {
        return __get(UserProps.DESCRIPTION.unwrap());
    }

    @Override
    public PropExpression.Str gender() {
        return __get(UserProps.GENDER.unwrap());
    }

    @Override
    public PropExpression.Str home() {
        return __get(UserProps.HOME.unwrap());
    }

    @Override
    public PropExpression.Str status() {
        return __get(UserProps.STATUS.unwrap());
    }

    @Override
    public Predicate types(Function<TypeTableEx, Predicate> block) {
        return exists(UserProps.TYPES.unwrap(), block);
    }

    @Override
    public Predicate adores(Function<UserTableEx, Predicate> block) {
        return exists(UserProps.ADORES.unwrap(), block);
    }

    @Override
    public Predicate followers(Function<UserTableEx, Predicate> block) {
        return exists(UserProps.FOLLOWERS.unwrap(), block);
    }

    @Override
    public Predicate roles(Function<RoleTableEx, Predicate> block) {
        return exists(UserProps.ROLES.unwrap(), block);
    }

    @Override
    public Predicate videos(Function<VideoTableEx, Predicate> block) {
        return exists(UserProps.VIDEOS.unwrap(), block);
    }

    @Override
    public Predicate collections(Function<CollectionTableEx, Predicate> block) {
        return exists(UserProps.COLLECTIONS.unwrap(), block);
    }

    @Override
    public UserTableEx asTableEx() {
        return new UserTableEx(this, null);
    }

    @Override
    public UserTable __disableJoin(String reason) {
        return new UserTable(this, reason);
    }

    @GeneratedBy(
            type = User.class
    )
    public static class Remote extends AbstractTypedTable<User> {
        public Remote(AbstractTypedTable.DelayedOperation delayedOperation) {
            super(User.class, delayedOperation);
        }

        public Remote(TableImplementor<User> table) {
            super(table);
        }

        public PropExpression.Num<Long> id() {
            return __get(UserProps.ID.unwrap());
        }

        @Override
        @Deprecated
        public TableEx<User> asTableEx() {
            throw new UnsupportedOperationException();
        }

        @Override
        public Remote __disableJoin(String reason) {
            return this;
        }
    }
}
