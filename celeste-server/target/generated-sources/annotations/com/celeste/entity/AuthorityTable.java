package com.celeste.entity;

import java.lang.Deprecated;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.sql.ast.PropExpression;
import org.babyfish.jimmer.sql.ast.impl.table.TableImplementor;
import org.babyfish.jimmer.sql.ast.table.TableEx;
import org.babyfish.jimmer.sql.ast.table.spi.AbstractTypedTable;

@GeneratedBy(
        type = Authority.class
)
public class AuthorityTable extends AbstractTypedTable<Authority> implements AuthorityProps {
    public static final AuthorityTable $ = new AuthorityTable();

    public AuthorityTable() {
        super(Authority.class);
    }

    public AuthorityTable(AbstractTypedTable.DelayedOperation<Authority> delayedOperation) {
        super(Authority.class, delayedOperation);
    }

    public AuthorityTable(TableImplementor<Authority> table) {
        super(table);
    }

    protected AuthorityTable(AuthorityTable base, String joinDisabledReason) {
        super(base, joinDisabledReason);
    }

    @Override
    public PropExpression.Num<Long> id() {
        return __get(AuthorityProps.ID.unwrap());
    }

    @Override
    public PropExpression.Str name() {
        return __get(AuthorityProps.NAME.unwrap());
    }

    @Override
    public AuthorityTableEx asTableEx() {
        return new AuthorityTableEx(this, null);
    }

    @Override
    public AuthorityTable __disableJoin(String reason) {
        return new AuthorityTable(this, reason);
    }

    @GeneratedBy(
            type = Authority.class
    )
    public static class Remote extends AbstractTypedTable<Authority> {
        public Remote(AbstractTypedTable.DelayedOperation delayedOperation) {
            super(Authority.class, delayedOperation);
        }

        public Remote(TableImplementor<Authority> table) {
            super(table);
        }

        public PropExpression.Num<Long> id() {
            return __get(AuthorityProps.ID.unwrap());
        }

        @Override
        @Deprecated
        public TableEx<Authority> asTableEx() {
            throw new UnsupportedOperationException();
        }

        @Override
        public Remote __disableJoin(String reason) {
            return this;
        }
    }
}
