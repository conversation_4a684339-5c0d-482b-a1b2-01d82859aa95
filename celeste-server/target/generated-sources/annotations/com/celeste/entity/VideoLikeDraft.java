package com.celeste.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import java.lang.CloneNotSupportedException;
import java.lang.Cloneable;
import java.lang.IllegalArgumentException;
import java.lang.IllegalStateException;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.lang.System;
import java.util.Collections;
import java.util.Objects;
import org.babyfish.jimmer.CircularReferenceException;
import org.babyfish.jimmer.Draft;
import org.babyfish.jimmer.DraftConsumer;
import org.babyfish.jimmer.ImmutableObjects;
import org.babyfish.jimmer.UnloadedException;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.jackson.ImmutableModuleRequiredException;
import org.babyfish.jimmer.lang.OldChain;
import org.babyfish.jimmer.meta.ImmutableType;
import org.babyfish.jimmer.meta.PropId;
import org.babyfish.jimmer.runtime.DraftContext;
import org.babyfish.jimmer.runtime.DraftSpi;
import org.babyfish.jimmer.runtime.ImmutableSpi;
import org.babyfish.jimmer.runtime.Internal;
import org.babyfish.jimmer.runtime.Visibility;
import org.babyfish.jimmer.sql.ManyToOne;

@GeneratedBy(
        type = VideoLike.class
)
public interface VideoLikeDraft extends VideoLike, Draft {
    VideoLikeDraft.Producer $ = Producer.INSTANCE;

    @OldChain
    VideoLikeDraft setId(long id);

    UserDraft user();

    UserDraft user(boolean autoCreate);

    @OldChain
    VideoLikeDraft setUser(User user);

    long userId();

    @OldChain
    VideoLikeDraft setUserId(long userId);

    @OldChain
    VideoLikeDraft applyUser(DraftConsumer<UserDraft> block);

    @OldChain
    VideoLikeDraft applyUser(User base, DraftConsumer<UserDraft> block);

    VideoDraft video();

    VideoDraft video(boolean autoCreate);

    @OldChain
    VideoLikeDraft setVideo(Video video);

    long videoId();

    @OldChain
    VideoLikeDraft setVideoId(long videoId);

    @OldChain
    VideoLikeDraft applyVideo(DraftConsumer<VideoDraft> block);

    @OldChain
    VideoLikeDraft applyVideo(Video base, DraftConsumer<VideoDraft> block);

    @GeneratedBy(
            type = VideoLike.class
    )
    class Producer {
        static final Producer INSTANCE = new Producer();

        public static final int SLOT_ID = 0;

        public static final int SLOT_USER = 1;

        public static final int SLOT_VIDEO = 2;

        public static final ImmutableType TYPE = ImmutableType
            .newBuilder(
                "0.8.150",
                VideoLike.class,
                Collections.emptyList(),
                (ctx, base) -> new DraftImpl(ctx, (VideoLike)base)
            )
            .id(SLOT_ID, "id", long.class)
            .add(SLOT_USER, "user", ManyToOne.class, User.class, false)
            .add(SLOT_VIDEO, "video", ManyToOne.class, Video.class, false)
            .build();

        private Producer() {
        }

        public VideoLike produce(DraftConsumer<VideoLikeDraft> block) {
            return produce(null, block);
        }

        public VideoLike produce(VideoLike base, DraftConsumer<VideoLikeDraft> block) {
            return (VideoLike)Internal.produce(TYPE, base, block);
        }

        @GeneratedBy(
                type = VideoLike.class
        )
        @JsonPropertyOrder({"dummyPropForJacksonError__", "id", "user", "video"})
        public abstract interface Implementor extends VideoLike, ImmutableSpi {
            @Override
            default Object __get(PropId prop) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __get(prop.asName());
                    case SLOT_ID:
                    		return (Long)id();
                    case SLOT_USER:
                    		return user();
                    case SLOT_VIDEO:
                    		return video();
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.VideoLike\": \"" + prop + "\"");
                }
            }

            @Override
            default Object __get(String prop) {
                switch (prop) {
                    case "id":
                    		return (Long)id();
                    case "user":
                    		return user();
                    case "video":
                    		return video();
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.VideoLike\": \"" + prop + "\"");
                }
            }

            default long getId() {
                return id();
            }

            default User getUser() {
                return user();
            }

            default Video getVideo() {
                return video();
            }

            @Override
            default ImmutableType __type() {
                return TYPE;
            }

            default int getDummyPropForJacksonError__() {
                throw new ImmutableModuleRequiredException();
            }
        }

        @GeneratedBy(
                type = VideoLike.class
        )
        private static class Impl implements Implementor, Cloneable, Serializable {
            private Visibility __visibility;

            long __idValue;

            boolean __idLoaded = false;

            User __userValue;

            Video __videoValue;

            @Override
            @JsonIgnore
            public long id() {
                if (!__idLoaded) {
                    throw new UnloadedException(VideoLike.class, "id");
                }
                return __idValue;
            }

            @Override
            @JsonIgnore
            public User user() {
                if (__userValue == null) {
                    throw new UnloadedException(VideoLike.class, "user");
                }
                return __userValue;
            }

            @Override
            @JsonIgnore
            public Video video() {
                if (__videoValue == null) {
                    throw new UnloadedException(VideoLike.class, "video");
                }
                return __videoValue;
            }

            @Override
            public Impl clone() {
                try {
                    return (Impl)super.clone();
                } catch(CloneNotSupportedException ex) {
                    throw new AssertionError(ex);
                }
            }

            @Override
            public boolean __isLoaded(PropId prop) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __isLoaded(prop.asName());
                    case SLOT_ID:
                    		return __idLoaded;
                    case SLOT_USER:
                    		return __userValue != null;
                    case SLOT_VIDEO:
                    		return __videoValue != null;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.VideoLike\": \"" + prop + "\"");
                }
            }

            @Override
            public boolean __isLoaded(String prop) {
                switch (prop) {
                    case "id":
                    		return __idLoaded;
                    case "user":
                    		return __userValue != null;
                    case "video":
                    		return __videoValue != null;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.VideoLike\": \"" + prop + "\"");
                }
            }

            @Override
            public boolean __isVisible(PropId prop) {
                if (__visibility == null) {
                    return true;
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __isVisible(prop.asName());
                    case SLOT_ID:
                    		return __visibility.visible(SLOT_ID);
                    case SLOT_USER:
                    		return __visibility.visible(SLOT_USER);
                    case SLOT_VIDEO:
                    		return __visibility.visible(SLOT_VIDEO);
                    default: return true;
                }
            }

            @Override
            public boolean __isVisible(String prop) {
                if (__visibility == null) {
                    return true;
                }
                switch (prop) {
                    case "id":
                    		return __visibility.visible(SLOT_ID);
                    case "user":
                    		return __visibility.visible(SLOT_USER);
                    case "video":
                    		return __visibility.visible(SLOT_VIDEO);
                    default: return true;
                }
            }

            @Override
            public int hashCode() {
                int hash = __visibility != null ? __visibility.hashCode() : 0;
                if (__idLoaded) {
                    hash = 31 * hash + Long.hashCode(__idValue);
                    // If entity-id is loaded, return directly
                    return hash;
                }
                if (__userValue != null) {
                    hash = 31 * hash + __userValue.hashCode();
                }
                if (__videoValue != null) {
                    hash = 31 * hash + __videoValue.hashCode();
                }
                return hash;
            }

            private int __shallowHashCode() {
                int hash = __visibility != null ? __visibility.hashCode() : 0;
                if (__idLoaded) {
                    hash = 31 * hash + Long.hashCode(__idValue);
                }
                if (__userValue != null) {
                    hash = 31 * hash + System.identityHashCode(__userValue);
                }
                if (__videoValue != null) {
                    hash = 31 * hash + System.identityHashCode(__videoValue);
                }
                return hash;
            }

            @Override
            public int __hashCode(boolean shallow) {
                return shallow ? __shallowHashCode() : hashCode();
            }

            @Override
            public boolean equals(Object obj) {
                if (obj == null || !(obj instanceof Implementor)) {
                    return false;
                }
                Implementor __other = (Implementor)obj;
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                boolean __idLoaded = this.__idLoaded;
                if (__idLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                if (__idLoaded) {
                    // If entity-id is loaded, return directly
                    return __idValue == __other.id();
                }
                if (__isVisible(PropId.byIndex(SLOT_USER)) != __other.__isVisible(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                boolean __userLoaded = __userValue != null;
                if (__userLoaded != __other.__isLoaded(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                if (__userLoaded && !Objects.equals(__userValue, __other.user())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_VIDEO)) != __other.__isVisible(PropId.byIndex(SLOT_VIDEO))) {
                    return false;
                }
                boolean __videoLoaded = __videoValue != null;
                if (__videoLoaded != __other.__isLoaded(PropId.byIndex(SLOT_VIDEO))) {
                    return false;
                }
                if (__videoLoaded && !Objects.equals(__videoValue, __other.video())) {
                    return false;
                }
                return true;
            }

            private boolean __shallowEquals(Object obj) {
                if (obj == null || !(obj instanceof Implementor)) {
                    return false;
                }
                Implementor __other = (Implementor)obj;
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                boolean __idLoaded = this.__idLoaded;
                if (__idLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                if (__idLoaded && __idValue != __other.id()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USER)) != __other.__isVisible(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                boolean __userLoaded = __userValue != null;
                if (__userLoaded != __other.__isLoaded(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                if (__userLoaded && __userValue != __other.user()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_VIDEO)) != __other.__isVisible(PropId.byIndex(SLOT_VIDEO))) {
                    return false;
                }
                boolean __videoLoaded = __videoValue != null;
                if (__videoLoaded != __other.__isLoaded(PropId.byIndex(SLOT_VIDEO))) {
                    return false;
                }
                if (__videoLoaded && __videoValue != __other.video()) {
                    return false;
                }
                return true;
            }

            @Override
            public boolean __equals(Object obj, boolean shallow) {
                return shallow ? __shallowEquals(obj) : equals(obj);
            }

            @Override
            public String toString() {
                return ImmutableObjects.toString(this);
            }
        }

        @GeneratedBy(
                type = VideoLike.class
        )
        private static class DraftImpl implements Implementor, DraftSpi, VideoLikeDraft {
            private DraftContext __ctx;

            private Impl __base;

            private Impl __modified;

            private boolean __resolving;

            private VideoLike __resolved;

            DraftImpl(DraftContext ctx, VideoLike base) {
                __ctx = ctx;
                if (base != null) {
                    __base = (Impl)base;
                }
                else {
                    __modified = new Impl();
                }
            }

            @Override
            public boolean __isLoaded(PropId prop) {
                return (__modified!= null ? __modified : __base).__isLoaded(prop);
            }

            @Override
            public boolean __isLoaded(String prop) {
                return (__modified!= null ? __modified : __base).__isLoaded(prop);
            }

            @Override
            public boolean __isVisible(PropId prop) {
                return (__modified!= null ? __modified : __base).__isVisible(prop);
            }

            @Override
            public boolean __isVisible(String prop) {
                return (__modified!= null ? __modified : __base).__isVisible(prop);
            }

            @Override
            public int hashCode() {
                return (__modified!= null ? __modified : __base).hashCode();
            }

            @Override
            public int __hashCode(boolean shallow) {
                return (__modified!= null ? __modified : __base).__hashCode(shallow);
            }

            @Override
            public boolean equals(Object obj) {
                return (__modified!= null ? __modified : __base).equals(obj);
            }

            @Override
            public boolean __equals(Object obj, boolean shallow) {
                return (__modified!= null ? __modified : __base).__equals(obj, shallow);
            }

            @Override
            public String toString() {
                return ImmutableObjects.toString(this);
            }

            @Override
            @JsonIgnore
            public long id() {
                return (__modified!= null ? __modified : __base).id();
            }

            @Override
            public VideoLikeDraft setId(long id) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__idValue = id;
                __tmpModified.__idLoaded = true;
                return this;
            }

            @Override
            @JsonIgnore
            public UserDraft user() {
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).user());
            }

            @Override
            public UserDraft user(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_USER)))) {
                    setUser(UserDraft.$.produce(null, null));
                }
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).user());
            }

            @Override
            public VideoLikeDraft setUser(User user) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (user == null) {
                    throw new IllegalArgumentException(
                        "'user' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__userValue = user;
                return this;
            }

            @Override
            public long userId() {
                return user().id();
            }

            @OldChain
            @Override
            public VideoLikeDraft setUserId(long userId) {
                user(true).setId(Objects.requireNonNull(userId, "\"user\" cannot be null"));
                return this;
            }

            @Override
            public VideoLikeDraft applyUser(DraftConsumer<UserDraft> block) {
                applyUser(null, block);
                return this;
            }

            @Override
            public VideoLikeDraft applyUser(User base, DraftConsumer<UserDraft> block) {
                setUser(UserDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public VideoDraft video() {
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).video());
            }

            @Override
            public VideoDraft video(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_VIDEO)))) {
                    setVideo(VideoDraft.$.produce(null, null));
                }
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).video());
            }

            @Override
            public VideoLikeDraft setVideo(Video video) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (video == null) {
                    throw new IllegalArgumentException(
                        "'video' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__videoValue = video;
                return this;
            }

            @Override
            public long videoId() {
                return video().id();
            }

            @OldChain
            @Override
            public VideoLikeDraft setVideoId(long videoId) {
                video(true).setId(Objects.requireNonNull(videoId, "\"video\" cannot be null"));
                return this;
            }

            @Override
            public VideoLikeDraft applyVideo(DraftConsumer<VideoDraft> block) {
                applyVideo(null, block);
                return this;
            }

            @Override
            public VideoLikeDraft applyVideo(Video base, DraftConsumer<VideoDraft> block) {
                setVideo(VideoDraft.$.produce(base, block));
                return this;
            }

            @SuppressWarnings("unchecked")
            @Override
            public void __set(PropId prop, Object value) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__set(prop.asName(), value);
                    return;
                    case SLOT_ID:
                    		if (value == null) throw new IllegalArgumentException("'id' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setId((Long)value);
                            break;
                    case SLOT_USER:
                    		setUser((User)value);break;
                    case SLOT_VIDEO:
                    		setVideo((Video)value);break;
                    default: throw new IllegalArgumentException("Illegal property id for \"com.celeste.entity.VideoLike\": \"" + prop + "\"");
                }
            }

            @SuppressWarnings("unchecked")
            @Override
            public void __set(String prop, Object value) {
                switch (prop) {
                    case "id":
                    		if (value == null) throw new IllegalArgumentException("'id' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setId((Long)value);
                            break;
                    case "user":
                    		setUser((User)value);break;
                    case "video":
                    		setVideo((Video)value);break;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.VideoLike\": \"" + prop + "\"");
                }
            }

            @Override
            public void __show(PropId prop, boolean visible) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Visibility __visibility = (__modified!= null ? __modified : __base).__visibility;
                if (__visibility == null) {
                    if (visible) {
                        return;
                    }
                    __modified().__visibility = __visibility = Visibility.of(3);
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__show(prop.asName(), visible);
                    return;
                    case SLOT_ID:
                    		__visibility.show(SLOT_ID, visible);break;
                    case SLOT_USER:
                    		__visibility.show(SLOT_USER, visible);break;
                    case SLOT_VIDEO:
                    		__visibility.show(SLOT_VIDEO, visible);break;
                    default: throw new IllegalArgumentException(
                                "Illegal property id for \"com.celeste.entity.VideoLike\": \"" + 
                                prop + 
                                "\",it does not exists"
                            );
                }
            }

            @Override
            public void __show(String prop, boolean visible) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Visibility __visibility = (__modified!= null ? __modified : __base).__visibility;
                if (__visibility == null) {
                    if (visible) {
                        return;
                    }
                    __modified().__visibility = __visibility = Visibility.of(3);
                }
                switch (prop) {
                    case "id":
                    		__visibility.show(SLOT_ID, visible);break;
                    case "user":
                    		__visibility.show(SLOT_USER, visible);break;
                    case "video":
                    		__visibility.show(SLOT_VIDEO, visible);break;
                    default: throw new IllegalArgumentException(
                                "Illegal property name for \"com.celeste.entity.VideoLike\": \"" + 
                                prop + 
                                "\",it does not exists"
                            );
                }
            }

            @Override
            public void __unload(PropId prop) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__unload(prop.asName());
                    return;
                    case SLOT_ID:
                    		__modified().__idLoaded = false;break;
                    case SLOT_USER:
                    		__modified().__userValue = null;break;
                    case SLOT_VIDEO:
                    		__modified().__videoValue = null;break;
                    default: throw new IllegalArgumentException("Illegal property id for \"com.celeste.entity.VideoLike\": \"" + prop + "\", it does not exist or its loaded state is not controllable");
                }
            }

            @Override
            public void __unload(String prop) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                switch (prop) {
                    case "id":
                    		__modified().__idLoaded = false;break;
                    case "user":
                    		__modified().__userValue = null;break;
                    case "video":
                    		__modified().__videoValue = null;break;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.VideoLike\": \"" + prop + "\", it does not exist or its loaded state is not controllable");
                }
            }

            @Override
            public DraftContext __draftContext() {
                return __ctx;
            }

            @Override
            public Object __resolve() {
                if (__resolved != null) {
                    return __resolved;
                }
                if (__resolving) {
                    throw new CircularReferenceException();
                }
                __resolving = true;
                try {
                    Implementor base = __base;
                    Impl __tmpModified = __modified;
                    if (__tmpModified == null) {
                        if (base.__isLoaded(PropId.byIndex(SLOT_USER))) {
                            User oldValue = base.user();
                            User newValue = __ctx.resolveObject(oldValue);
                            if (oldValue != newValue) {
                                setUser(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_VIDEO))) {
                            Video oldValue = base.video();
                            Video newValue = __ctx.resolveObject(oldValue);
                            if (oldValue != newValue) {
                                setVideo(newValue);
                            }
                        }
                        __tmpModified = __modified;
                    }
                    else {
                        __tmpModified.__userValue = __ctx.resolveObject(__tmpModified.__userValue);
                        __tmpModified.__videoValue = __ctx.resolveObject(__tmpModified.__videoValue);
                    }
                    if (__base != null && __tmpModified == null) {
                        this.__resolved = base;
                        return base;
                    }
                    this.__resolved = __tmpModified;
                    return __tmpModified;
                }
                finally {
                    __resolving = false;
                }
            }

            @Override
            public boolean __isResolved() {
                return __resolved != null;
            }

            Impl __modified() {
                Impl __tmpModified = __modified;
                if (__tmpModified == null) {
                    __tmpModified = __base.clone();
                    __modified = __tmpModified;
                }
                return __tmpModified;
            }
        }
    }

    @GeneratedBy(
            type = VideoLike.class
    )
    class Builder {
        private final Producer.DraftImpl __draft;

        public Builder() {
            __draft = new Producer.DraftImpl(null, null);
        }

        public Builder id(Long id) {
            if (id != null) {
                __draft.setId(id);
            }
            return this;
        }

        public Builder user(User user) {
            if (user != null) {
                __draft.setUser(user);
            }
            return this;
        }

        public Builder video(Video video) {
            if (video != null) {
                __draft.setVideo(video);
            }
            return this;
        }

        public VideoLike build() {
            return (VideoLike)__draft.__modified();
        }
    }
}
