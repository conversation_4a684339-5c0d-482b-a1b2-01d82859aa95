package com.celeste.entity;

import java.lang.Long;
import java.lang.String;
import java.util.Date;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.meta.ImmutableType;
import org.babyfish.jimmer.meta.TypedProp;
import org.babyfish.jimmer.sql.JoinType;
import org.babyfish.jimmer.sql.ast.PropExpression;
import org.babyfish.jimmer.sql.ast.table.PropsFor;

@GeneratedBy(
        type = Message.class
)
@PropsFor(Message.class)
public interface MessageProps extends BaseProps {
    TypedProp.Scalar<Message, Long> ID = 
        TypedProp.scalar(ImmutableType.get(Message.class).getProp("id"));

    TypedProp.Scalar<Message, Date> CREATE_TIME = 
        TypedProp.scalar(ImmutableType.get(Message.class).getProp("createTime"));

    TypedProp.Scalar<Message, Date> MODIFY_TIME = 
        TypedProp.scalar(ImmutableType.get(Message.class).getProp("modifyTime"));

    TypedProp.Scalar<Message, String> BETWEEN_TIME = 
        TypedProp.scalar(ImmutableType.get(Message.class).getProp("betweenTime"));

    TypedProp.Reference<Message, User> SENDER = 
        TypedProp.reference(ImmutableType.get(Message.class).getProp("sender"));

    TypedProp.Scalar<Message, Long> SENDER_ID = 
        TypedProp.scalar(ImmutableType.get(Message.class).getProp("senderId"));

    TypedProp.Reference<Message, User> RECIPIENT = 
        TypedProp.reference(ImmutableType.get(Message.class).getProp("recipient"));

    TypedProp.Scalar<Message, Long> RECIPIENT_ID = 
        TypedProp.scalar(ImmutableType.get(Message.class).getProp("recipientId"));

    TypedProp.Scalar<Message, String> CONTENT = 
        TypedProp.scalar(ImmutableType.get(Message.class).getProp("content"));

    TypedProp.Scalar<Message, String> IMAGES = 
        TypedProp.scalar(ImmutableType.get(Message.class).getProp("images"));

    PropExpression.Num<Long> id();

    UserTable sender();

    UserTable sender(JoinType joinType);

    PropExpression.Num<Long> senderId();

    UserTable recipient();

    UserTable recipient(JoinType joinType);

    PropExpression.Num<Long> recipientId();

    PropExpression.Str content();

    PropExpression.Str images();
}
