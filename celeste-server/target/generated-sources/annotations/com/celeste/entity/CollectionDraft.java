package com.celeste.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import java.lang.CloneNotSupportedException;
import java.lang.Cloneable;
import java.lang.IllegalArgumentException;
import java.lang.IllegalStateException;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.lang.System;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.babyfish.jimmer.CircularReferenceException;
import org.babyfish.jimmer.DraftConsumer;
import org.babyfish.jimmer.ImmutableObjects;
import org.babyfish.jimmer.UnloadedException;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.jackson.ImmutableModuleRequiredException;
import org.babyfish.jimmer.lang.OldChain;
import org.babyfish.jimmer.meta.ImmutablePropCategory;
import org.babyfish.jimmer.meta.ImmutableType;
import org.babyfish.jimmer.meta.PropId;
import org.babyfish.jimmer.runtime.DraftContext;
import org.babyfish.jimmer.runtime.DraftSpi;
import org.babyfish.jimmer.runtime.ImmutableSpi;
import org.babyfish.jimmer.runtime.Internal;
import org.babyfish.jimmer.runtime.NonSharedList;
import org.babyfish.jimmer.runtime.Visibility;
import org.babyfish.jimmer.sql.ManyToMany;
import org.babyfish.jimmer.sql.ManyToOne;
import org.babyfish.jimmer.sql.collection.IdViewList;
import org.babyfish.jimmer.sql.collection.MutableIdViewList;
import org.jetbrains.annotations.Nullable;

@GeneratedBy(
        type = Collection.class
)
public interface CollectionDraft extends Collection, BaseDraft {
    CollectionDraft.Producer $ = Producer.INSTANCE;

    @OldChain
    CollectionDraft setId(long id);

    @OldChain
    CollectionDraft setCreateTime(Date createTime);

    @OldChain
    CollectionDraft setModifyTime(Date modifyTime);

    @OldChain
    CollectionDraft setName(String name);

    UserDraft user();

    UserDraft user(boolean autoCreate);

    @OldChain
    CollectionDraft setUser(User user);

    long userId();

    @OldChain
    CollectionDraft setUserId(long userId);

    @OldChain
    CollectionDraft applyUser(DraftConsumer<UserDraft> block);

    @OldChain
    CollectionDraft applyUser(User base, DraftConsumer<UserDraft> block);

    List<VideoDraft> videos(boolean autoCreate);

    @OldChain
    CollectionDraft setVideos(List<Video> videos);

    @OldChain
    CollectionDraft addIntoVideos(DraftConsumer<VideoDraft> block);

    @OldChain
    CollectionDraft addIntoVideos(Video base, DraftConsumer<VideoDraft> block);

    List<Long> videoIds(boolean autoCreate);

    @OldChain
    CollectionDraft setVideoIds(List<Long> videoIds);

    @GeneratedBy(
            type = Collection.class
    )
    class Producer {
        static final Producer INSTANCE = new Producer();

        public static final int SLOT_ID = 3;

        public static final int SLOT_CREATE_TIME = 0;

        public static final int SLOT_MODIFY_TIME = 1;

        public static final int SLOT_BETWEEN_TIME = 2;

        public static final int SLOT_NAME = 4;

        public static final int SLOT_USER = 5;

        public static final int SLOT_VIDEOS = 6;

        public static final int SLOT_VIDEO_IDS = 7;

        public static final int SLOT_VIDEOS_SUM = 8;

        public static final ImmutableType TYPE = ImmutableType
            .newBuilder(
                "0.8.150",
                Collection.class,
                Collections.singleton(BaseDraft.Producer.TYPE),
                (ctx, base) -> new DraftImpl(ctx, (Collection)base)
            )
            .redefine("createTime", SLOT_CREATE_TIME)
            .redefine("modifyTime", SLOT_MODIFY_TIME)
            .redefine("betweenTime", SLOT_BETWEEN_TIME)
            .id(SLOT_ID, "id", long.class)
            .add(SLOT_NAME, "name", ImmutablePropCategory.SCALAR, String.class, false)
            .add(SLOT_USER, "user", ManyToOne.class, User.class, false)
            .add(SLOT_VIDEOS, "videos", ManyToMany.class, Video.class, false)
            .add(SLOT_VIDEO_IDS, "videoIds", ImmutablePropCategory.SCALAR_LIST, Long.class, false)
            .add(SLOT_VIDEOS_SUM, "videosSum", ImmutablePropCategory.SCALAR, long.class, false)
            .build();

        private Producer() {
        }

        public Collection produce(DraftConsumer<CollectionDraft> block) {
            return produce(null, block);
        }

        public Collection produce(Collection base, DraftConsumer<CollectionDraft> block) {
            return (Collection)Internal.produce(TYPE, base, block);
        }

        @GeneratedBy(
                type = Collection.class
        )
        @JsonPropertyOrder({"dummyPropForJacksonError__", "createTime", "modifyTime", "betweenTime", "id", "name", "user", "videos", "videoIds", "videosSum"})
        public abstract interface Implementor extends Collection, ImmutableSpi {
            @Override
            default Object __get(PropId prop) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __get(prop.asName());
                    case SLOT_CREATE_TIME:
                    		return createTime();
                    case SLOT_MODIFY_TIME:
                    		return modifyTime();
                    case SLOT_BETWEEN_TIME:
                    		return betweenTime();
                    case SLOT_ID:
                    		return (Long)id();
                    case SLOT_NAME:
                    		return name();
                    case SLOT_USER:
                    		return user();
                    case SLOT_VIDEOS:
                    		return videos();
                    case SLOT_VIDEO_IDS:
                    		return videoIds();
                    case SLOT_VIDEOS_SUM:
                    		return (Long)videosSum();
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Collection\": \"" + prop + "\"");
                }
            }

            @Override
            default Object __get(String prop) {
                switch (prop) {
                    case "createTime":
                    		return createTime();
                    case "modifyTime":
                    		return modifyTime();
                    case "betweenTime":
                    		return betweenTime();
                    case "id":
                    		return (Long)id();
                    case "name":
                    		return name();
                    case "user":
                    		return user();
                    case "videos":
                    		return videos();
                    case "videoIds":
                    		return videoIds();
                    case "videosSum":
                    		return (Long)videosSum();
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Collection\": \"" + prop + "\"");
                }
            }

            default long getId() {
                return id();
            }

            default Date getCreateTime() {
                return createTime();
            }

            @Nullable
            default Date getModifyTime() {
                return modifyTime();
            }

            default String getBetweenTime() {
                return betweenTime();
            }

            default String getName() {
                return name();
            }

            default User getUser() {
                return user();
            }

            default List<Video> getVideos() {
                return videos();
            }

            default List<Long> getVideoIds() {
                return videoIds();
            }

            default long getVideosSum() {
                return videosSum();
            }

            @Override
            default ImmutableType __type() {
                return TYPE;
            }

            default int getDummyPropForJacksonError__() {
                throw new ImmutableModuleRequiredException();
            }
        }

        @GeneratedBy(
                type = Collection.class
        )
        private static class Impl implements Implementor, Cloneable, Serializable {
            private Visibility __visibility;

            long __idValue;

            boolean __idLoaded = false;

            Date __createTimeValue;

            Date __modifyTimeValue;

            boolean __modifyTimeLoaded = false;

            String __nameValue;

            User __userValue;

            NonSharedList<Video> __videosValue;

            Impl() {
                __visibility = Visibility.of(9);
                __visibility.show(SLOT_BETWEEN_TIME, false);
                __visibility.show(SLOT_VIDEO_IDS, false);
                __visibility.show(SLOT_VIDEOS_SUM, false);
            }

            @Override
            @JsonIgnore
            public long id() {
                if (!__idLoaded) {
                    throw new UnloadedException(Collection.class, "id");
                }
                return __idValue;
            }

            @Override
            @JsonIgnore
            public Date createTime() {
                if (__createTimeValue == null) {
                    throw new UnloadedException(Collection.class, "createTime");
                }
                return __createTimeValue;
            }

            @Override
            @JsonIgnore
            @Nullable
            public Date modifyTime() {
                if (!__modifyTimeLoaded) {
                    throw new UnloadedException(Collection.class, "modifyTime");
                }
                return __modifyTimeValue;
            }

            @Override
            @JsonIgnore
            public String name() {
                if (__nameValue == null) {
                    throw new UnloadedException(Collection.class, "name");
                }
                return __nameValue;
            }

            @Override
            @JsonIgnore
            public User user() {
                if (__userValue == null) {
                    throw new UnloadedException(Collection.class, "user");
                }
                return __userValue;
            }

            @Override
            @JsonIgnore
            public List<Video> videos() {
                if (__videosValue == null) {
                    throw new UnloadedException(Collection.class, "videos");
                }
                return __videosValue;
            }

            @Override
            @JsonIgnore
            public List<Long> videoIds() {
                return new IdViewList<>(VideoDraft.Producer.TYPE, videos());
            }

            @Override
            public Impl clone() {
                try {
                    return (Impl)super.clone();
                } catch(CloneNotSupportedException ex) {
                    throw new AssertionError(ex);
                }
            }

            @Override
            public boolean __isLoaded(PropId prop) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __isLoaded(prop.asName());
                    case SLOT_CREATE_TIME:
                    		return __createTimeValue != null;
                    case SLOT_MODIFY_TIME:
                    		return __modifyTimeLoaded;
                    case SLOT_BETWEEN_TIME:
                    		return __isLoaded(PropId.byIndex(SLOT_CREATE_TIME));
                    case SLOT_ID:
                    		return __idLoaded;
                    case SLOT_NAME:
                    		return __nameValue != null;
                    case SLOT_USER:
                    		return __userValue != null;
                    case SLOT_VIDEOS:
                    		return __videosValue != null;
                    case SLOT_VIDEO_IDS:
                    		return __isLoaded(PropId.byIndex(SLOT_VIDEOS)) && videos().stream().allMatch(__each -> 
                                ((ImmutableSpi)__each).__isLoaded(PropId.byIndex(VideoDraft.Producer.SLOT_ID))
                            );
                    case SLOT_VIDEOS_SUM:
                    		return __isLoaded(PropId.byIndex(SLOT_VIDEOS));
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Collection\": \"" + prop + "\"");
                }
            }

            @Override
            public boolean __isLoaded(String prop) {
                switch (prop) {
                    case "createTime":
                    		return __createTimeValue != null;
                    case "modifyTime":
                    		return __modifyTimeLoaded;
                    case "betweenTime":
                    		return __isLoaded(PropId.byIndex(SLOT_CREATE_TIME));
                    case "id":
                    		return __idLoaded;
                    case "name":
                    		return __nameValue != null;
                    case "user":
                    		return __userValue != null;
                    case "videos":
                    		return __videosValue != null;
                    case "videoIds":
                    		return __isLoaded(PropId.byIndex(SLOT_VIDEOS)) && videos().stream().allMatch(__each -> 
                                ((ImmutableSpi)__each).__isLoaded(PropId.byIndex(VideoDraft.Producer.SLOT_ID))
                            );
                    case "videosSum":
                    		return __isLoaded(PropId.byIndex(SLOT_VIDEOS));
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Collection\": \"" + prop + "\"");
                }
            }

            @Override
            public boolean __isVisible(PropId prop) {
                if (__visibility == null) {
                    return true;
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __isVisible(prop.asName());
                    case SLOT_CREATE_TIME:
                    		return __visibility.visible(SLOT_CREATE_TIME);
                    case SLOT_MODIFY_TIME:
                    		return __visibility.visible(SLOT_MODIFY_TIME);
                    case SLOT_BETWEEN_TIME:
                    		return __visibility.visible(SLOT_BETWEEN_TIME);
                    case SLOT_ID:
                    		return __visibility.visible(SLOT_ID);
                    case SLOT_NAME:
                    		return __visibility.visible(SLOT_NAME);
                    case SLOT_USER:
                    		return __visibility.visible(SLOT_USER);
                    case SLOT_VIDEOS:
                    		return __visibility.visible(SLOT_VIDEOS);
                    case SLOT_VIDEO_IDS:
                    		return __visibility.visible(SLOT_VIDEO_IDS);
                    case SLOT_VIDEOS_SUM:
                    		return __visibility.visible(SLOT_VIDEOS_SUM);
                    default: return true;
                }
            }

            @Override
            public boolean __isVisible(String prop) {
                if (__visibility == null) {
                    return true;
                }
                switch (prop) {
                    case "createTime":
                    		return __visibility.visible(SLOT_CREATE_TIME);
                    case "modifyTime":
                    		return __visibility.visible(SLOT_MODIFY_TIME);
                    case "betweenTime":
                    		return __visibility.visible(SLOT_BETWEEN_TIME);
                    case "id":
                    		return __visibility.visible(SLOT_ID);
                    case "name":
                    		return __visibility.visible(SLOT_NAME);
                    case "user":
                    		return __visibility.visible(SLOT_USER);
                    case "videos":
                    		return __visibility.visible(SLOT_VIDEOS);
                    case "videoIds":
                    		return __visibility.visible(SLOT_VIDEO_IDS);
                    case "videosSum":
                    		return __visibility.visible(SLOT_VIDEOS_SUM);
                    default: return true;
                }
            }

            @Override
            public int hashCode() {
                int hash = __visibility != null ? __visibility.hashCode() : 0;
                if (__idLoaded) {
                    hash = 31 * hash + Long.hashCode(__idValue);
                    // If entity-id is loaded, return directly
                    return hash;
                }
                if (__createTimeValue != null) {
                    hash = 31 * hash + __createTimeValue.hashCode();
                }
                if (__modifyTimeLoaded && __modifyTimeValue != null) {
                    hash = 31 * hash + __modifyTimeValue.hashCode();
                }
                if (__nameValue != null) {
                    hash = 31 * hash + __nameValue.hashCode();
                }
                if (__userValue != null) {
                    hash = 31 * hash + __userValue.hashCode();
                }
                if (__videosValue != null) {
                    hash = 31 * hash + __videosValue.hashCode();
                }
                return hash;
            }

            private int __shallowHashCode() {
                int hash = __visibility != null ? __visibility.hashCode() : 0;
                if (__idLoaded) {
                    hash = 31 * hash + Long.hashCode(__idValue);
                }
                if (__createTimeValue != null) {
                    hash = 31 * hash + System.identityHashCode(__createTimeValue);
                }
                if (__modifyTimeLoaded) {
                    hash = 31 * hash + System.identityHashCode(__modifyTimeValue);
                }
                if (__nameValue != null) {
                    hash = 31 * hash + System.identityHashCode(__nameValue);
                }
                if (__userValue != null) {
                    hash = 31 * hash + System.identityHashCode(__userValue);
                }
                if (__videosValue != null) {
                    hash = 31 * hash + System.identityHashCode(__videosValue);
                }
                return hash;
            }

            @Override
            public int __hashCode(boolean shallow) {
                return shallow ? __shallowHashCode() : hashCode();
            }

            @Override
            public boolean equals(Object obj) {
                if (obj == null || !(obj instanceof Implementor)) {
                    return false;
                }
                Implementor __other = (Implementor)obj;
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                boolean __idLoaded = this.__idLoaded;
                if (__idLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                if (__idLoaded) {
                    // If entity-id is loaded, return directly
                    return __idValue == __other.id();
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATE_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                boolean __createTimeLoaded = __createTimeValue != null;
                if (__createTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                if (__createTimeLoaded && !Objects.equals(__createTimeValue, __other.createTime())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_MODIFY_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                boolean __modifyTimeLoaded = this.__modifyTimeLoaded;
                if (__modifyTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                if (__modifyTimeLoaded && !Objects.equals(__modifyTimeValue, __other.modifyTime())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_NAME))) {
                    return false;
                }
                boolean __nameLoaded = __nameValue != null;
                if (__nameLoaded != __other.__isLoaded(PropId.byIndex(SLOT_NAME))) {
                    return false;
                }
                if (__nameLoaded && !Objects.equals(__nameValue, __other.name())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USER)) != __other.__isVisible(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                boolean __userLoaded = __userValue != null;
                if (__userLoaded != __other.__isLoaded(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                if (__userLoaded && !Objects.equals(__userValue, __other.user())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_VIDEOS)) != __other.__isVisible(PropId.byIndex(SLOT_VIDEOS))) {
                    return false;
                }
                boolean __videosLoaded = __videosValue != null;
                if (__videosLoaded != __other.__isLoaded(PropId.byIndex(SLOT_VIDEOS))) {
                    return false;
                }
                if (__videosLoaded && !Objects.equals(__videosValue, __other.videos())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_VIDEO_IDS)) != __other.__isVisible(PropId.byIndex(SLOT_VIDEO_IDS))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_VIDEOS_SUM)) != __other.__isVisible(PropId.byIndex(SLOT_VIDEOS_SUM))) {
                    return false;
                }
                return true;
            }

            private boolean __shallowEquals(Object obj) {
                if (obj == null || !(obj instanceof Implementor)) {
                    return false;
                }
                Implementor __other = (Implementor)obj;
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                boolean __idLoaded = this.__idLoaded;
                if (__idLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                if (__idLoaded && __idValue != __other.id()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATE_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                boolean __createTimeLoaded = __createTimeValue != null;
                if (__createTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                if (__createTimeLoaded && __createTimeValue != __other.createTime()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_MODIFY_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                boolean __modifyTimeLoaded = this.__modifyTimeLoaded;
                if (__modifyTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                if (__modifyTimeLoaded && __modifyTimeValue != __other.modifyTime()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_NAME))) {
                    return false;
                }
                boolean __nameLoaded = __nameValue != null;
                if (__nameLoaded != __other.__isLoaded(PropId.byIndex(SLOT_NAME))) {
                    return false;
                }
                if (__nameLoaded && __nameValue != __other.name()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USER)) != __other.__isVisible(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                boolean __userLoaded = __userValue != null;
                if (__userLoaded != __other.__isLoaded(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                if (__userLoaded && __userValue != __other.user()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_VIDEOS)) != __other.__isVisible(PropId.byIndex(SLOT_VIDEOS))) {
                    return false;
                }
                boolean __videosLoaded = __videosValue != null;
                if (__videosLoaded != __other.__isLoaded(PropId.byIndex(SLOT_VIDEOS))) {
                    return false;
                }
                if (__videosLoaded && __videosValue != __other.videos()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_VIDEO_IDS)) != __other.__isVisible(PropId.byIndex(SLOT_VIDEO_IDS))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_VIDEOS_SUM)) != __other.__isVisible(PropId.byIndex(SLOT_VIDEOS_SUM))) {
                    return false;
                }
                return true;
            }

            @Override
            public boolean __equals(Object obj, boolean shallow) {
                return shallow ? __shallowEquals(obj) : equals(obj);
            }

            @Override
            public String toString() {
                return ImmutableObjects.toString(this);
            }
        }

        @GeneratedBy(
                type = Collection.class
        )
        private static class DraftImpl implements Implementor, DraftSpi, CollectionDraft {
            private DraftContext __ctx;

            private Impl __base;

            private Impl __modified;

            private boolean __resolving;

            private Collection __resolved;

            DraftImpl(DraftContext ctx, Collection base) {
                __ctx = ctx;
                if (base != null) {
                    __base = (Impl)base;
                }
                else {
                    __modified = new Impl();
                }
            }

            @Override
            public boolean __isLoaded(PropId prop) {
                return (__modified!= null ? __modified : __base).__isLoaded(prop);
            }

            @Override
            public boolean __isLoaded(String prop) {
                return (__modified!= null ? __modified : __base).__isLoaded(prop);
            }

            @Override
            public boolean __isVisible(PropId prop) {
                return (__modified!= null ? __modified : __base).__isVisible(prop);
            }

            @Override
            public boolean __isVisible(String prop) {
                return (__modified!= null ? __modified : __base).__isVisible(prop);
            }

            @Override
            public int hashCode() {
                return (__modified!= null ? __modified : __base).hashCode();
            }

            @Override
            public int __hashCode(boolean shallow) {
                return (__modified!= null ? __modified : __base).__hashCode(shallow);
            }

            @Override
            public boolean equals(Object obj) {
                return (__modified!= null ? __modified : __base).equals(obj);
            }

            @Override
            public boolean __equals(Object obj, boolean shallow) {
                return (__modified!= null ? __modified : __base).__equals(obj, shallow);
            }

            @Override
            public String toString() {
                return ImmutableObjects.toString(this);
            }

            @Override
            @JsonIgnore
            public long id() {
                return (__modified!= null ? __modified : __base).id();
            }

            @Override
            public CollectionDraft setId(long id) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__idValue = id;
                __tmpModified.__idLoaded = true;
                return this;
            }

            @Override
            @JsonIgnore
            public Date createTime() {
                return (__modified!= null ? __modified : __base).createTime();
            }

            @Override
            public CollectionDraft setCreateTime(Date createTime) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (createTime == null) {
                    throw new IllegalArgumentException(
                        "'createTime' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__createTimeValue = createTime;
                return this;
            }

            @Override
            @JsonIgnore
            @Nullable
            public Date modifyTime() {
                return (__modified!= null ? __modified : __base).modifyTime();
            }

            @Override
            public CollectionDraft setModifyTime(Date modifyTime) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__modifyTimeValue = modifyTime;
                __tmpModified.__modifyTimeLoaded = true;
                return this;
            }

            @Override
            @JsonIgnore
            public String betweenTime() {
                return (__modified!= null ? __modified : __base).betweenTime();
            }

            @Override
            @JsonIgnore
            public String name() {
                return (__modified!= null ? __modified : __base).name();
            }

            @Override
            public CollectionDraft setName(String name) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (name == null) {
                    throw new IllegalArgumentException(
                        "'name' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__nameValue = name;
                return this;
            }

            @Override
            @JsonIgnore
            public UserDraft user() {
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).user());
            }

            @Override
            public UserDraft user(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_USER)))) {
                    setUser(UserDraft.$.produce(null, null));
                }
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).user());
            }

            @Override
            public CollectionDraft setUser(User user) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (user == null) {
                    throw new IllegalArgumentException(
                        "'user' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__userValue = user;
                return this;
            }

            @Override
            public long userId() {
                return user().id();
            }

            @OldChain
            @Override
            public CollectionDraft setUserId(long userId) {
                user(true).setId(Objects.requireNonNull(userId, "\"user\" cannot be null"));
                return this;
            }

            @Override
            public CollectionDraft applyUser(DraftConsumer<UserDraft> block) {
                applyUser(null, block);
                return this;
            }

            @Override
            public CollectionDraft applyUser(User base, DraftConsumer<UserDraft> block) {
                setUser(UserDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public List<Video> videos() {
                return __ctx.toDraftList((__modified!= null ? __modified : __base).videos(), Video.class, true);
            }

            @Override
            public List<VideoDraft> videos(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_VIDEOS)))) {
                    setVideos(new ArrayList<>());
                }
                return __ctx.toDraftList((__modified!= null ? __modified : __base).videos(), Video.class, true);
            }

            @Override
            public CollectionDraft setVideos(List<Video> videos) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (videos == null) {
                    throw new IllegalArgumentException(
                        "'videos' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__videosValue = NonSharedList.of(__tmpModified.__videosValue, videos);
                return this;
            }

            @Override
            public CollectionDraft addIntoVideos(DraftConsumer<VideoDraft> block) {
                addIntoVideos(null, block);
                return this;
            }

            @Override
            public CollectionDraft addIntoVideos(Video base, DraftConsumer<VideoDraft> block) {
                videos(true).add((VideoDraft)VideoDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public List<Long> videoIds() {
                List<Long> __ids = new ArrayList(videos().size());
                for (Video __target : videos()) {
                    __ids.add(__target.id());
                }
                return __ids;
            }

            @Override
            public List<Long> videoIds(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_VIDEOS)))) {
                    setVideos(new ArrayList<>());
                }
                return new MutableIdViewList<>(VideoDraft.Producer.TYPE, videos());
            }

            @Override
            public CollectionDraft setVideoIds(List<Long> videoIds) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (videoIds != null) {
                    List<Video> __targets = new ArrayList(videoIds.size());
                    for (long __id : videoIds) {
                        __targets.add(ImmutableObjects.makeIdOnly(Video.class, __id));
                    }
                    setVideos(__targets);
                } else {
                    setVideos(Collections.emptyList());
                }
                return this;
            }

            @Override
            @JsonIgnore
            public long videosSum() {
                return (__modified!= null ? __modified : __base).videosSum();
            }

            @SuppressWarnings("unchecked")
            @Override
            public void __set(PropId prop, Object value) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__set(prop.asName(), value);
                    return;
                    case SLOT_CREATE_TIME:
                    		setCreateTime((Date)value);break;
                    case SLOT_MODIFY_TIME:
                    		setModifyTime((Date)value);break;
                    case SLOT_BETWEEN_TIME:
                    		break;
                    case SLOT_ID:
                    		if (value == null) throw new IllegalArgumentException("'id' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setId((Long)value);
                            break;
                    case SLOT_NAME:
                    		setName((String)value);break;
                    case SLOT_USER:
                    		setUser((User)value);break;
                    case SLOT_VIDEOS:
                    		setVideos((List<Video>)value);break;
                    case SLOT_VIDEO_IDS:
                    		setVideoIds((List<Long>)value);break;
                    case SLOT_VIDEOS_SUM:
                    		break;
                    default: throw new IllegalArgumentException("Illegal property id for \"com.celeste.entity.Collection\": \"" + prop + "\"");
                }
            }

            @SuppressWarnings("unchecked")
            @Override
            public void __set(String prop, Object value) {
                switch (prop) {
                    case "createTime":
                    		setCreateTime((Date)value);break;
                    case "modifyTime":
                    		setModifyTime((Date)value);break;
                    case "betweenTime":
                    		break;
                    case "id":
                    		if (value == null) throw new IllegalArgumentException("'id' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setId((Long)value);
                            break;
                    case "name":
                    		setName((String)value);break;
                    case "user":
                    		setUser((User)value);break;
                    case "videos":
                    		setVideos((List<Video>)value);break;
                    case "videoIds":
                    		setVideoIds((List<Long>)value);break;
                    case "videosSum":
                    		break;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Collection\": \"" + prop + "\"");
                }
            }

            @Override
            public void __show(PropId prop, boolean visible) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Visibility __visibility = (__modified!= null ? __modified : __base).__visibility;
                if (__visibility == null) {
                    if (visible) {
                        return;
                    }
                    __modified().__visibility = __visibility = Visibility.of(9);
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__show(prop.asName(), visible);
                    return;
                    case SLOT_CREATE_TIME:
                    		__visibility.show(SLOT_CREATE_TIME, visible);break;
                    case SLOT_MODIFY_TIME:
                    		__visibility.show(SLOT_MODIFY_TIME, visible);break;
                    case SLOT_BETWEEN_TIME:
                    		__visibility.show(SLOT_BETWEEN_TIME, visible);break;
                    case SLOT_ID:
                    		__visibility.show(SLOT_ID, visible);break;
                    case SLOT_NAME:
                    		__visibility.show(SLOT_NAME, visible);break;
                    case SLOT_USER:
                    		__visibility.show(SLOT_USER, visible);break;
                    case SLOT_VIDEOS:
                    		__visibility.show(SLOT_VIDEOS, visible);break;
                    case SLOT_VIDEO_IDS:
                    		__visibility.show(SLOT_VIDEO_IDS, visible);break;
                    case SLOT_VIDEOS_SUM:
                    		__visibility.show(SLOT_VIDEOS_SUM, visible);break;
                    default: throw new IllegalArgumentException(
                                "Illegal property id for \"com.celeste.entity.Collection\": \"" + 
                                prop + 
                                "\",it does not exists"
                            );
                }
            }

            @Override
            public void __show(String prop, boolean visible) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Visibility __visibility = (__modified!= null ? __modified : __base).__visibility;
                if (__visibility == null) {
                    if (visible) {
                        return;
                    }
                    __modified().__visibility = __visibility = Visibility.of(9);
                }
                switch (prop) {
                    case "createTime":
                    		__visibility.show(SLOT_CREATE_TIME, visible);break;
                    case "modifyTime":
                    		__visibility.show(SLOT_MODIFY_TIME, visible);break;
                    case "betweenTime":
                    		__visibility.show(SLOT_BETWEEN_TIME, visible);break;
                    case "id":
                    		__visibility.show(SLOT_ID, visible);break;
                    case "name":
                    		__visibility.show(SLOT_NAME, visible);break;
                    case "user":
                    		__visibility.show(SLOT_USER, visible);break;
                    case "videos":
                    		__visibility.show(SLOT_VIDEOS, visible);break;
                    case "videoIds":
                    		__visibility.show(SLOT_VIDEO_IDS, visible);break;
                    case "videosSum":
                    		__visibility.show(SLOT_VIDEOS_SUM, visible);break;
                    default: throw new IllegalArgumentException(
                                "Illegal property name for \"com.celeste.entity.Collection\": \"" + 
                                prop + 
                                "\",it does not exists"
                            );
                }
            }

            @Override
            public void __unload(PropId prop) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__unload(prop.asName());
                    return;
                    case SLOT_CREATE_TIME:
                    		__modified().__createTimeValue = null;break;
                    case SLOT_MODIFY_TIME:
                    		__modified().__modifyTimeLoaded = false;break;
                    case SLOT_BETWEEN_TIME:
                    		break;
                    case SLOT_ID:
                    		__modified().__idLoaded = false;break;
                    case SLOT_NAME:
                    		__modified().__nameValue = null;break;
                    case SLOT_USER:
                    		__modified().__userValue = null;break;
                    case SLOT_VIDEOS:
                    		__modified().__videosValue = null;break;
                    case SLOT_VIDEO_IDS:
                    		__unload(PropId.byIndex(SLOT_VIDEOS));break;
                    case SLOT_VIDEOS_SUM:
                    		break;
                    default: throw new IllegalArgumentException("Illegal property id for \"com.celeste.entity.Collection\": \"" + prop + "\", it does not exist or its loaded state is not controllable");
                }
            }

            @Override
            public void __unload(String prop) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                switch (prop) {
                    case "createTime":
                    		__modified().__createTimeValue = null;break;
                    case "modifyTime":
                    		__modified().__modifyTimeLoaded = false;break;
                    case "betweenTime":
                    		break;
                    case "id":
                    		__modified().__idLoaded = false;break;
                    case "name":
                    		__modified().__nameValue = null;break;
                    case "user":
                    		__modified().__userValue = null;break;
                    case "videos":
                    		__modified().__videosValue = null;break;
                    case "videoIds":
                    		__unload(PropId.byIndex(SLOT_VIDEOS));break;
                    case "videosSum":
                    		break;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Collection\": \"" + prop + "\", it does not exist or its loaded state is not controllable");
                }
            }

            @Override
            public DraftContext __draftContext() {
                return __ctx;
            }

            @Override
            public Object __resolve() {
                if (__resolved != null) {
                    return __resolved;
                }
                if (__resolving) {
                    throw new CircularReferenceException();
                }
                __resolving = true;
                try {
                    Implementor base = __base;
                    Impl __tmpModified = __modified;
                    if (__tmpModified == null) {
                        if (base.__isLoaded(PropId.byIndex(SLOT_USER))) {
                            User oldValue = base.user();
                            User newValue = __ctx.resolveObject(oldValue);
                            if (oldValue != newValue) {
                                setUser(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_VIDEOS))) {
                            List<Video> oldValue = base.videos();
                            List<Video> newValue = __ctx.resolveList(oldValue);
                            if (oldValue != newValue) {
                                setVideos(newValue);
                            }
                        }
                        __tmpModified = __modified;
                    }
                    else {
                        __tmpModified.__userValue = __ctx.resolveObject(__tmpModified.__userValue);
                        __tmpModified.__videosValue = NonSharedList.of(__tmpModified.__videosValue, __ctx.resolveList(__tmpModified.__videosValue));
                    }
                    if (__base != null && __tmpModified == null) {
                        this.__resolved = base;
                        return base;
                    }
                    this.__resolved = __tmpModified;
                    return __tmpModified;
                }
                finally {
                    __resolving = false;
                }
            }

            @Override
            public boolean __isResolved() {
                return __resolved != null;
            }

            Impl __modified() {
                Impl __tmpModified = __modified;
                if (__tmpModified == null) {
                    __tmpModified = __base.clone();
                    __modified = __tmpModified;
                }
                return __tmpModified;
            }
        }
    }

    @GeneratedBy(
            type = Collection.class
    )
    class Builder {
        private final Producer.DraftImpl __draft;

        public Builder() {
            __draft = new Producer.DraftImpl(null, null);
            __draft.__show(PropId.byIndex(Producer.SLOT_CREATE_TIME), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_BETWEEN_TIME), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_VIDEOS), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_VIDEO_IDS), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_VIDEOS_SUM), false);
        }

        public Builder id(Long id) {
            if (id != null) {
                __draft.setId(id);
            }
            return this;
        }

        public Builder createTime(Date createTime) {
            if (createTime != null) {
                __draft.setCreateTime(createTime);
                __draft.__show(PropId.byIndex(Producer.SLOT_CREATE_TIME), true);
            }
            return this;
        }

        @Nullable
        public Builder modifyTime(Date modifyTime) {
            __draft.setModifyTime(modifyTime);
            return this;
        }

        public Builder name(String name) {
            if (name != null) {
                __draft.setName(name);
            }
            return this;
        }

        public Builder user(User user) {
            if (user != null) {
                __draft.setUser(user);
            }
            return this;
        }

        public Builder videos(List<Video> videos) {
            if (videos != null) {
                __draft.setVideos(videos);
                __draft.__show(PropId.byIndex(Producer.SLOT_VIDEOS), true);
            }
            return this;
        }

        public Builder videoIds(List<Long> videoIds) {
            if (videoIds != null) {
                __draft.setVideoIds(videoIds);
                __draft.__show(PropId.byIndex(Producer.SLOT_VIDEO_IDS), true);
            }
            return this;
        }

        public Collection build() {
            return (Collection)__draft.__modified();
        }
    }
}
