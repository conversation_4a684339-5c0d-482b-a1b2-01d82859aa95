package com.celeste.entity;

import java.lang.Boolean;
import java.lang.Long;
import java.lang.String;
import java.util.Date;
import java.util.function.Function;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.meta.ImmutableType;
import org.babyfish.jimmer.meta.TypedProp;
import org.babyfish.jimmer.sql.ast.Predicate;
import org.babyfish.jimmer.sql.ast.PropExpression;
import org.babyfish.jimmer.sql.ast.table.PropsFor;

@GeneratedBy(
        type = User.class
)
@PropsFor(User.class)
public interface UserProps extends BaseProps {
    TypedProp.Scalar<User, Long> ID = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("id"));

    TypedProp.Scalar<User, Date> CREATE_TIME = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("createTime"));

    TypedProp.Scalar<User, Date> MODIFY_TIME = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("modifyTime"));

    TypedProp.Scalar<User, String> BETWEEN_TIME = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("betweenTime"));

    TypedProp.Scalar<User, String> USERNAME = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("username"));

    TypedProp.Scalar<User, String> PASSWORD = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("password"));

    TypedProp.Scalar<User, String> EMAIL = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("email"));

    TypedProp.Scalar<User, String> DESCRIPTION = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("description"));

    TypedProp.Scalar<User, String> GENDER = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("gender"));

    TypedProp.Scalar<User, String> HOME = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("home"));

    TypedProp.Scalar<User, String> STATUS = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("status"));

    TypedProp.ReferenceList<User, Type> TYPES = 
        TypedProp.referenceList(ImmutableType.get(User.class).getProp("types"));

    TypedProp.ReferenceList<User, User> ADORES = 
        TypedProp.referenceList(ImmutableType.get(User.class).getProp("adores"));

    TypedProp.ReferenceList<User, User> FOLLOWERS = 
        TypedProp.referenceList(ImmutableType.get(User.class).getProp("followers"));

    TypedProp.ReferenceList<User, Role> ROLES = 
        TypedProp.referenceList(ImmutableType.get(User.class).getProp("roles"));

    TypedProp.ReferenceList<User, Video> VIDEOS = 
        TypedProp.referenceList(ImmutableType.get(User.class).getProp("videos"));

    TypedProp.ReferenceList<User, Collection> COLLECTIONS = 
        TypedProp.referenceList(ImmutableType.get(User.class).getProp("collections"));

    TypedProp.ScalarList<User, Long> TYPE_IDS = 
        TypedProp.scalarList(ImmutableType.get(User.class).getProp("typeIds"));

    TypedProp.ScalarList<User, Long> COLLECTED_VIDEOS = 
        TypedProp.scalarList(ImmutableType.get(User.class).getProp("collectedVideos"));

    TypedProp.Scalar<User, Long> ADORES_SUM = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("adoresSum"));

    TypedProp.Scalar<User, Long> FOLLOWERS_SUM = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("followersSum"));

    TypedProp.ScalarList<User, String> ROLE_LIST = 
        TypedProp.scalarList(ImmutableType.get(User.class).getProp("roleList"));

    TypedProp.ScalarList<User, String> AUTHORITY_LIST = 
        TypedProp.scalarList(ImmutableType.get(User.class).getProp("authorityList"));

    TypedProp.Scalar<User, Boolean> USER_IS_ADORE = 
        TypedProp.scalar(ImmutableType.get(User.class).getProp("userIsAdore"));

    PropExpression.Num<Long> id();

    PropExpression.Str username();

    PropExpression.Str password();

    PropExpression.Str email();

    PropExpression.Str description();

    PropExpression.Str gender();

    PropExpression.Str home();

    PropExpression.Str status();

    Predicate types(Function<TypeTableEx, Predicate> block);

    Predicate adores(Function<UserTableEx, Predicate> block);

    Predicate followers(Function<UserTableEx, Predicate> block);

    Predicate roles(Function<RoleTableEx, Predicate> block);

    Predicate videos(Function<VideoTableEx, Predicate> block);

    Predicate collections(Function<CollectionTableEx, Predicate> block);
}
