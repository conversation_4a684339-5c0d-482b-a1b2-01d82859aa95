package com.celeste.entity;

import java.lang.Override;
import java.util.function.Consumer;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.lang.NewChain;
import org.babyfish.jimmer.meta.ImmutableProp;
import org.babyfish.jimmer.sql.ast.table.Table;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.babyfish.jimmer.sql.fetcher.FieldConfig;
import org.babyfish.jimmer.sql.fetcher.IdOnlyFetchType;
import org.babyfish.jimmer.sql.fetcher.ListFieldConfig;
import org.babyfish.jimmer.sql.fetcher.impl.FetcherImpl;
import org.babyfish.jimmer.sql.fetcher.spi.AbstractTypedFetcher;

@GeneratedBy(
        type = Collection.class
)
public class CollectionFetcher extends AbstractTypedFetcher<Collection, CollectionFetcher> {
    public static final CollectionFetcher $ = new CollectionFetcher(null);

    private CollectionFetcher(FetcherImpl<Collection> base) {
        super(Collection.class, base);
    }

    private CollectionFetcher(CollectionFetcher prev, ImmutableProp prop, boolean negative,
            IdOnlyFetchType idOnlyFetchType) {
        super(prev, prop, negative, idOnlyFetchType);
    }

    private CollectionFetcher(CollectionFetcher prev, ImmutableProp prop,
            FieldConfig<?, ? extends Table<?>> fieldConfig) {
        super(prev, prop, fieldConfig);
    }

    public static CollectionFetcher $from(Fetcher<Collection> base) {
        return base instanceof CollectionFetcher ? 
        	(CollectionFetcher)base : 
        	new CollectionFetcher((FetcherImpl<Collection>)base);
    }

    @NewChain
    public CollectionFetcher createTime() {
        return add("createTime");
    }

    @NewChain
    public CollectionFetcher createTime(boolean enabled) {
        return enabled ? add("createTime") : remove("createTime");
    }

    @NewChain
    public CollectionFetcher modifyTime() {
        return add("modifyTime");
    }

    @NewChain
    public CollectionFetcher modifyTime(boolean enabled) {
        return enabled ? add("modifyTime") : remove("modifyTime");
    }

    @NewChain
    public CollectionFetcher betweenTime() {
        return add("betweenTime");
    }

    @NewChain
    public CollectionFetcher betweenTime(boolean enabled) {
        return enabled ? add("betweenTime") : remove("betweenTime");
    }

    @NewChain
    public CollectionFetcher name() {
        return add("name");
    }

    @NewChain
    public CollectionFetcher name(boolean enabled) {
        return enabled ? add("name") : remove("name");
    }

    @NewChain
    public CollectionFetcher user() {
        return add("user");
    }

    @NewChain
    public CollectionFetcher user(boolean enabled) {
        return enabled ? add("user") : remove("user");
    }

    @NewChain
    public CollectionFetcher user(Fetcher<User> childFetcher) {
        return add("user", childFetcher);
    }

    @NewChain
    public CollectionFetcher user(Fetcher<User> childFetcher,
            Consumer<FieldConfig<User, UserTable>> fieldConfig) {
        return add("user", childFetcher, fieldConfig);
    }

    @NewChain
    public CollectionFetcher user(IdOnlyFetchType idOnlyFetchType) {
        return add("user", idOnlyFetchType);
    }

    @NewChain
    public CollectionFetcher videos() {
        return add("videos");
    }

    @NewChain
    public CollectionFetcher videos(boolean enabled) {
        return enabled ? add("videos") : remove("videos");
    }

    @NewChain
    public CollectionFetcher videos(Fetcher<Video> childFetcher) {
        return add("videos", childFetcher);
    }

    @NewChain
    public CollectionFetcher videos(Fetcher<Video> childFetcher,
            Consumer<ListFieldConfig<Video, VideoTable>> fieldConfig) {
        return add("videos", childFetcher, fieldConfig);
    }

    @NewChain
    public CollectionFetcher videos(IdOnlyFetchType idOnlyFetchType) {
        return add("videos", idOnlyFetchType);
    }

    @NewChain
    public CollectionFetcher videoIds() {
        return add("videoIds");
    }

    @NewChain
    public CollectionFetcher videoIds(boolean enabled) {
        return enabled ? add("videoIds") : remove("videoIds");
    }

    @NewChain
    public CollectionFetcher videoIds(IdOnlyFetchType idOnlyFetchType) {
        return add("videoIds", idOnlyFetchType);
    }

    @NewChain
    public CollectionFetcher videosSum() {
        return add("videosSum");
    }

    @NewChain
    public CollectionFetcher videosSum(boolean enabled) {
        return enabled ? add("videosSum") : remove("videosSum");
    }

    @Override
    protected CollectionFetcher createFetcher(ImmutableProp prop, boolean negative,
            IdOnlyFetchType idOnlyFetchType) {
        return new CollectionFetcher(this, prop, negative, idOnlyFetchType);
    }

    @Override
    protected CollectionFetcher createFetcher(ImmutableProp prop,
            FieldConfig<?, ? extends Table<?>> fieldConfig) {
        return new CollectionFetcher(this, prop, fieldConfig);
    }
}
