package com.celeste.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import java.lang.Boolean;
import java.lang.CloneNotSupportedException;
import java.lang.Cloneable;
import java.lang.IllegalArgumentException;
import java.lang.IllegalStateException;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.lang.System;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.babyfish.jimmer.CircularReferenceException;
import org.babyfish.jimmer.DraftConsumer;
import org.babyfish.jimmer.ImmutableObjects;
import org.babyfish.jimmer.UnloadedException;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.jackson.ImmutableModuleRequiredException;
import org.babyfish.jimmer.lang.OldChain;
import org.babyfish.jimmer.meta.ImmutablePropCategory;
import org.babyfish.jimmer.meta.ImmutableType;
import org.babyfish.jimmer.meta.PropId;
import org.babyfish.jimmer.runtime.DraftContext;
import org.babyfish.jimmer.runtime.DraftSpi;
import org.babyfish.jimmer.runtime.ImmutableSpi;
import org.babyfish.jimmer.runtime.Internal;
import org.babyfish.jimmer.runtime.NonSharedList;
import org.babyfish.jimmer.runtime.Visibility;
import org.babyfish.jimmer.sql.ManyToOne;
import org.babyfish.jimmer.sql.OneToMany;
import org.babyfish.jimmer.sql.OneToOne;
import org.jetbrains.annotations.Nullable;

@GeneratedBy(
        type = Comment.class
)
public interface CommentDraft extends Comment, BaseDraft {
    CommentDraft.Producer $ = Producer.INSTANCE;

    @OldChain
    CommentDraft setId(long id);

    @OldChain
    CommentDraft setCreateTime(Date createTime);

    @OldChain
    CommentDraft setModifyTime(Date modifyTime);

    @OldChain
    CommentDraft setContent(String content);

    List<CommentLikeDraft> likes(boolean autoCreate);

    @OldChain
    CommentDraft setLikes(List<CommentLike> likes);

    @OldChain
    CommentDraft addIntoLikes(DraftConsumer<CommentLikeDraft> block);

    @OldChain
    CommentDraft addIntoLikes(CommentLike base, DraftConsumer<CommentLikeDraft> block);

    UserDraft user();

    UserDraft user(boolean autoCreate);

    @OldChain
    CommentDraft setUser(User user);

    long userId();

    @OldChain
    CommentDraft setUserId(long userId);

    @OldChain
    CommentDraft applyUser(DraftConsumer<UserDraft> block);

    @OldChain
    CommentDraft applyUser(User base, DraftConsumer<UserDraft> block);

    VideoDraft video();

    VideoDraft video(boolean autoCreate);

    @OldChain
    CommentDraft setVideo(Video video);

    long videoId();

    @OldChain
    CommentDraft setVideoId(long videoId);

    @OldChain
    CommentDraft applyVideo(DraftConsumer<VideoDraft> block);

    @OldChain
    CommentDraft applyVideo(Video base, DraftConsumer<VideoDraft> block);

    @Nullable
    CommentDraft root();

    CommentDraft root(boolean autoCreate);

    @OldChain
    CommentDraft setRoot(Comment root);

    @Nullable
    Long rootId();

    @OldChain
    CommentDraft setRootId(@Nullable Long rootId);

    @OldChain
    CommentDraft applyRoot(DraftConsumer<CommentDraft> block);

    @OldChain
    CommentDraft applyRoot(Comment base, DraftConsumer<CommentDraft> block);

    @Nullable
    CommentDraft parent();

    CommentDraft parent(boolean autoCreate);

    @OldChain
    CommentDraft setParent(Comment parent);

    @Nullable
    Long parentId();

    @OldChain
    CommentDraft setParentId(@Nullable Long parentId);

    @OldChain
    CommentDraft applyParent(DraftConsumer<CommentDraft> block);

    @OldChain
    CommentDraft applyParent(Comment base, DraftConsumer<CommentDraft> block);

    List<CommentDraft> children(boolean autoCreate);

    @OldChain
    CommentDraft setChildren(List<Comment> children);

    @OldChain
    CommentDraft addIntoChildren(DraftConsumer<CommentDraft> block);

    @OldChain
    CommentDraft addIntoChildren(Comment base, DraftConsumer<CommentDraft> block);

    @GeneratedBy(
            type = Comment.class
    )
    class Producer {
        static final Producer INSTANCE = new Producer();

        public static final int SLOT_ID = 3;

        public static final int SLOT_CREATE_TIME = 0;

        public static final int SLOT_MODIFY_TIME = 1;

        public static final int SLOT_BETWEEN_TIME = 2;

        public static final int SLOT_CONTENT = 4;

        public static final int SLOT_LIKES = 5;

        public static final int SLOT_USER = 6;

        public static final int SLOT_VIDEO = 7;

        public static final int SLOT_ROOT = 8;

        public static final int SLOT_PARENT = 9;

        public static final int SLOT_CHILDREN = 10;

        public static final int SLOT_LIKES_SUM = 11;

        public static final int SLOT_CHILDREN_SUM = 12;

        public static final int SLOT_USER_IS_LIKE = 13;

        public static final ImmutableType TYPE = ImmutableType
            .newBuilder(
                "0.8.150",
                Comment.class,
                Collections.singleton(BaseDraft.Producer.TYPE),
                (ctx, base) -> new DraftImpl(ctx, (Comment)base)
            )
            .redefine("createTime", SLOT_CREATE_TIME)
            .redefine("modifyTime", SLOT_MODIFY_TIME)
            .redefine("betweenTime", SLOT_BETWEEN_TIME)
            .id(SLOT_ID, "id", long.class)
            .add(SLOT_CONTENT, "content", ImmutablePropCategory.SCALAR, String.class, false)
            .add(SLOT_LIKES, "likes", OneToMany.class, CommentLike.class, false)
            .add(SLOT_USER, "user", ManyToOne.class, User.class, false)
            .add(SLOT_VIDEO, "video", ManyToOne.class, Video.class, false)
            .add(SLOT_ROOT, "root", ManyToOne.class, Comment.class, true)
            .add(SLOT_PARENT, "parent", OneToOne.class, Comment.class, true)
            .add(SLOT_CHILDREN, "children", OneToMany.class, Comment.class, false)
            .add(SLOT_LIKES_SUM, "likesSum", ImmutablePropCategory.SCALAR, long.class, false)
            .add(SLOT_CHILDREN_SUM, "childrenSum", ImmutablePropCategory.SCALAR, long.class, false)
            .add(SLOT_USER_IS_LIKE, "userIsLike", ImmutablePropCategory.SCALAR, boolean.class, false)
            .build();

        private Producer() {
        }

        public Comment produce(DraftConsumer<CommentDraft> block) {
            return produce(null, block);
        }

        public Comment produce(Comment base, DraftConsumer<CommentDraft> block) {
            return (Comment)Internal.produce(TYPE, base, block);
        }

        @GeneratedBy(
                type = Comment.class
        )
        @JsonPropertyOrder({"dummyPropForJacksonError__", "createTime", "modifyTime", "betweenTime", "id", "content", "likes", "user", "video", "root", "parent", "children", "likesSum", "childrenSum", "userIsLike"})
        public abstract interface Implementor extends Comment, ImmutableSpi {
            @Override
            default Object __get(PropId prop) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __get(prop.asName());
                    case SLOT_CREATE_TIME:
                    		return createTime();
                    case SLOT_MODIFY_TIME:
                    		return modifyTime();
                    case SLOT_BETWEEN_TIME:
                    		return betweenTime();
                    case SLOT_ID:
                    		return (Long)id();
                    case SLOT_CONTENT:
                    		return content();
                    case SLOT_LIKES:
                    		return likes();
                    case SLOT_USER:
                    		return user();
                    case SLOT_VIDEO:
                    		return video();
                    case SLOT_ROOT:
                    		return root();
                    case SLOT_PARENT:
                    		return parent();
                    case SLOT_CHILDREN:
                    		return children();
                    case SLOT_LIKES_SUM:
                    		return (Long)likesSum();
                    case SLOT_CHILDREN_SUM:
                    		return (Long)childrenSum();
                    case SLOT_USER_IS_LIKE:
                    		return (Boolean)userIsLike();
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Comment\": \"" + prop + "\"");
                }
            }

            @Override
            default Object __get(String prop) {
                switch (prop) {
                    case "createTime":
                    		return createTime();
                    case "modifyTime":
                    		return modifyTime();
                    case "betweenTime":
                    		return betweenTime();
                    case "id":
                    		return (Long)id();
                    case "content":
                    		return content();
                    case "likes":
                    		return likes();
                    case "user":
                    		return user();
                    case "video":
                    		return video();
                    case "root":
                    		return root();
                    case "parent":
                    		return parent();
                    case "children":
                    		return children();
                    case "likesSum":
                    		return (Long)likesSum();
                    case "childrenSum":
                    		return (Long)childrenSum();
                    case "userIsLike":
                    		return (Boolean)userIsLike();
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Comment\": \"" + prop + "\"");
                }
            }

            default long getId() {
                return id();
            }

            default Date getCreateTime() {
                return createTime();
            }

            @Nullable
            default Date getModifyTime() {
                return modifyTime();
            }

            default String getBetweenTime() {
                return betweenTime();
            }

            default String getContent() {
                return content();
            }

            default List<CommentLike> getLikes() {
                return likes();
            }

            default User getUser() {
                return user();
            }

            default Video getVideo() {
                return video();
            }

            @Nullable
            default Comment getRoot() {
                return root();
            }

            @Nullable
            default Comment getParent() {
                return parent();
            }

            default List<Comment> getChildren() {
                return children();
            }

            default long getLikesSum() {
                return likesSum();
            }

            default long getChildrenSum() {
                return childrenSum();
            }

            default boolean isUserIsLike() {
                return userIsLike();
            }

            @Override
            default ImmutableType __type() {
                return TYPE;
            }

            default int getDummyPropForJacksonError__() {
                throw new ImmutableModuleRequiredException();
            }
        }

        @GeneratedBy(
                type = Comment.class
        )
        private static class Impl implements Implementor, Cloneable, Serializable {
            private Visibility __visibility;

            long __idValue;

            boolean __idLoaded = false;

            Date __createTimeValue;

            Date __modifyTimeValue;

            boolean __modifyTimeLoaded = false;

            String __contentValue;

            NonSharedList<CommentLike> __likesValue;

            User __userValue;

            Video __videoValue;

            Comment __rootValue;

            boolean __rootLoaded = false;

            Comment __parentValue;

            boolean __parentLoaded = false;

            NonSharedList<Comment> __childrenValue;

            Impl() {
                __visibility = Visibility.of(14);
                __visibility.show(SLOT_BETWEEN_TIME, false);
                __visibility.show(SLOT_LIKES_SUM, false);
                __visibility.show(SLOT_CHILDREN_SUM, false);
                __visibility.show(SLOT_USER_IS_LIKE, false);
            }

            @Override
            @JsonIgnore
            public long id() {
                if (!__idLoaded) {
                    throw new UnloadedException(Comment.class, "id");
                }
                return __idValue;
            }

            @Override
            @JsonIgnore
            public Date createTime() {
                if (__createTimeValue == null) {
                    throw new UnloadedException(Comment.class, "createTime");
                }
                return __createTimeValue;
            }

            @Override
            @JsonIgnore
            @Nullable
            public Date modifyTime() {
                if (!__modifyTimeLoaded) {
                    throw new UnloadedException(Comment.class, "modifyTime");
                }
                return __modifyTimeValue;
            }

            @Override
            @JsonIgnore
            public String content() {
                if (__contentValue == null) {
                    throw new UnloadedException(Comment.class, "content");
                }
                return __contentValue;
            }

            @Override
            @JsonIgnore
            public List<CommentLike> likes() {
                if (__likesValue == null) {
                    throw new UnloadedException(Comment.class, "likes");
                }
                return __likesValue;
            }

            @Override
            @JsonIgnore
            public User user() {
                if (__userValue == null) {
                    throw new UnloadedException(Comment.class, "user");
                }
                return __userValue;
            }

            @Override
            @JsonIgnore
            public Video video() {
                if (__videoValue == null) {
                    throw new UnloadedException(Comment.class, "video");
                }
                return __videoValue;
            }

            @Override
            @JsonIgnore
            @Nullable
            public Comment root() {
                if (!__rootLoaded) {
                    throw new UnloadedException(Comment.class, "root");
                }
                return __rootValue;
            }

            @Override
            @JsonIgnore
            @Nullable
            public Comment parent() {
                if (!__parentLoaded) {
                    throw new UnloadedException(Comment.class, "parent");
                }
                return __parentValue;
            }

            @Override
            @JsonIgnore
            public List<Comment> children() {
                if (__childrenValue == null) {
                    throw new UnloadedException(Comment.class, "children");
                }
                return __childrenValue;
            }

            @Override
            public Impl clone() {
                try {
                    return (Impl)super.clone();
                } catch(CloneNotSupportedException ex) {
                    throw new AssertionError(ex);
                }
            }

            @Override
            public boolean __isLoaded(PropId prop) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __isLoaded(prop.asName());
                    case SLOT_CREATE_TIME:
                    		return __createTimeValue != null;
                    case SLOT_MODIFY_TIME:
                    		return __modifyTimeLoaded;
                    case SLOT_BETWEEN_TIME:
                    		return __isLoaded(PropId.byIndex(SLOT_CREATE_TIME));
                    case SLOT_ID:
                    		return __idLoaded;
                    case SLOT_CONTENT:
                    		return __contentValue != null;
                    case SLOT_LIKES:
                    		return __likesValue != null;
                    case SLOT_USER:
                    		return __userValue != null;
                    case SLOT_VIDEO:
                    		return __videoValue != null;
                    case SLOT_ROOT:
                    		return __rootLoaded;
                    case SLOT_PARENT:
                    		return __parentLoaded;
                    case SLOT_CHILDREN:
                    		return __childrenValue != null;
                    case SLOT_LIKES_SUM:
                    		return __isLoaded(PropId.byIndex(SLOT_LIKES));
                    case SLOT_CHILDREN_SUM:
                    		return __isLoaded(PropId.byIndex(SLOT_CHILDREN));
                    case SLOT_USER_IS_LIKE:
                    		return ImmutableObjects.isLoadedChain(this, PropId.byIndex(Producer.SLOT_LIKES), PropId.byIndex(CommentLikeDraft.Producer.SLOT_USER), PropId.byIndex(UserDraft.Producer.SLOT_ID));
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Comment\": \"" + prop + "\"");
                }
            }

            @Override
            public boolean __isLoaded(String prop) {
                switch (prop) {
                    case "createTime":
                    		return __createTimeValue != null;
                    case "modifyTime":
                    		return __modifyTimeLoaded;
                    case "betweenTime":
                    		return __isLoaded(PropId.byIndex(SLOT_CREATE_TIME));
                    case "id":
                    		return __idLoaded;
                    case "content":
                    		return __contentValue != null;
                    case "likes":
                    		return __likesValue != null;
                    case "user":
                    		return __userValue != null;
                    case "video":
                    		return __videoValue != null;
                    case "root":
                    		return __rootLoaded;
                    case "parent":
                    		return __parentLoaded;
                    case "children":
                    		return __childrenValue != null;
                    case "likesSum":
                    		return __isLoaded(PropId.byIndex(SLOT_LIKES));
                    case "childrenSum":
                    		return __isLoaded(PropId.byIndex(SLOT_CHILDREN));
                    case "userIsLike":
                    		return ImmutableObjects.isLoadedChain(this, PropId.byIndex(Producer.SLOT_LIKES), PropId.byIndex(CommentLikeDraft.Producer.SLOT_USER), PropId.byIndex(UserDraft.Producer.SLOT_ID));
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Comment\": \"" + prop + "\"");
                }
            }

            @Override
            public boolean __isVisible(PropId prop) {
                if (__visibility == null) {
                    return true;
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __isVisible(prop.asName());
                    case SLOT_CREATE_TIME:
                    		return __visibility.visible(SLOT_CREATE_TIME);
                    case SLOT_MODIFY_TIME:
                    		return __visibility.visible(SLOT_MODIFY_TIME);
                    case SLOT_BETWEEN_TIME:
                    		return __visibility.visible(SLOT_BETWEEN_TIME);
                    case SLOT_ID:
                    		return __visibility.visible(SLOT_ID);
                    case SLOT_CONTENT:
                    		return __visibility.visible(SLOT_CONTENT);
                    case SLOT_LIKES:
                    		return __visibility.visible(SLOT_LIKES);
                    case SLOT_USER:
                    		return __visibility.visible(SLOT_USER);
                    case SLOT_VIDEO:
                    		return __visibility.visible(SLOT_VIDEO);
                    case SLOT_ROOT:
                    		return __visibility.visible(SLOT_ROOT);
                    case SLOT_PARENT:
                    		return __visibility.visible(SLOT_PARENT);
                    case SLOT_CHILDREN:
                    		return __visibility.visible(SLOT_CHILDREN);
                    case SLOT_LIKES_SUM:
                    		return __visibility.visible(SLOT_LIKES_SUM);
                    case SLOT_CHILDREN_SUM:
                    		return __visibility.visible(SLOT_CHILDREN_SUM);
                    case SLOT_USER_IS_LIKE:
                    		return __visibility.visible(SLOT_USER_IS_LIKE);
                    default: return true;
                }
            }

            @Override
            public boolean __isVisible(String prop) {
                if (__visibility == null) {
                    return true;
                }
                switch (prop) {
                    case "createTime":
                    		return __visibility.visible(SLOT_CREATE_TIME);
                    case "modifyTime":
                    		return __visibility.visible(SLOT_MODIFY_TIME);
                    case "betweenTime":
                    		return __visibility.visible(SLOT_BETWEEN_TIME);
                    case "id":
                    		return __visibility.visible(SLOT_ID);
                    case "content":
                    		return __visibility.visible(SLOT_CONTENT);
                    case "likes":
                    		return __visibility.visible(SLOT_LIKES);
                    case "user":
                    		return __visibility.visible(SLOT_USER);
                    case "video":
                    		return __visibility.visible(SLOT_VIDEO);
                    case "root":
                    		return __visibility.visible(SLOT_ROOT);
                    case "parent":
                    		return __visibility.visible(SLOT_PARENT);
                    case "children":
                    		return __visibility.visible(SLOT_CHILDREN);
                    case "likesSum":
                    		return __visibility.visible(SLOT_LIKES_SUM);
                    case "childrenSum":
                    		return __visibility.visible(SLOT_CHILDREN_SUM);
                    case "userIsLike":
                    		return __visibility.visible(SLOT_USER_IS_LIKE);
                    default: return true;
                }
            }

            @Override
            public int hashCode() {
                int hash = __visibility != null ? __visibility.hashCode() : 0;
                if (__idLoaded) {
                    hash = 31 * hash + Long.hashCode(__idValue);
                    // If entity-id is loaded, return directly
                    return hash;
                }
                if (__createTimeValue != null) {
                    hash = 31 * hash + __createTimeValue.hashCode();
                }
                if (__modifyTimeLoaded && __modifyTimeValue != null) {
                    hash = 31 * hash + __modifyTimeValue.hashCode();
                }
                if (__contentValue != null) {
                    hash = 31 * hash + __contentValue.hashCode();
                }
                if (__likesValue != null) {
                    hash = 31 * hash + __likesValue.hashCode();
                }
                if (__userValue != null) {
                    hash = 31 * hash + __userValue.hashCode();
                }
                if (__videoValue != null) {
                    hash = 31 * hash + __videoValue.hashCode();
                }
                if (__rootLoaded && __rootValue != null) {
                    hash = 31 * hash + __rootValue.hashCode();
                }
                if (__parentLoaded && __parentValue != null) {
                    hash = 31 * hash + __parentValue.hashCode();
                }
                if (__childrenValue != null) {
                    hash = 31 * hash + __childrenValue.hashCode();
                }
                return hash;
            }

            private int __shallowHashCode() {
                int hash = __visibility != null ? __visibility.hashCode() : 0;
                if (__idLoaded) {
                    hash = 31 * hash + Long.hashCode(__idValue);
                }
                if (__createTimeValue != null) {
                    hash = 31 * hash + System.identityHashCode(__createTimeValue);
                }
                if (__modifyTimeLoaded) {
                    hash = 31 * hash + System.identityHashCode(__modifyTimeValue);
                }
                if (__contentValue != null) {
                    hash = 31 * hash + System.identityHashCode(__contentValue);
                }
                if (__likesValue != null) {
                    hash = 31 * hash + System.identityHashCode(__likesValue);
                }
                if (__userValue != null) {
                    hash = 31 * hash + System.identityHashCode(__userValue);
                }
                if (__videoValue != null) {
                    hash = 31 * hash + System.identityHashCode(__videoValue);
                }
                if (__rootLoaded) {
                    hash = 31 * hash + System.identityHashCode(__rootValue);
                }
                if (__parentLoaded) {
                    hash = 31 * hash + System.identityHashCode(__parentValue);
                }
                if (__childrenValue != null) {
                    hash = 31 * hash + System.identityHashCode(__childrenValue);
                }
                return hash;
            }

            @Override
            public int __hashCode(boolean shallow) {
                return shallow ? __shallowHashCode() : hashCode();
            }

            @Override
            public boolean equals(Object obj) {
                if (obj == null || !(obj instanceof Implementor)) {
                    return false;
                }
                Implementor __other = (Implementor)obj;
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                boolean __idLoaded = this.__idLoaded;
                if (__idLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                if (__idLoaded) {
                    // If entity-id is loaded, return directly
                    return __idValue == __other.id();
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATE_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                boolean __createTimeLoaded = __createTimeValue != null;
                if (__createTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                if (__createTimeLoaded && !Objects.equals(__createTimeValue, __other.createTime())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_MODIFY_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                boolean __modifyTimeLoaded = this.__modifyTimeLoaded;
                if (__modifyTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                if (__modifyTimeLoaded && !Objects.equals(__modifyTimeValue, __other.modifyTime())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_CONTENT)) != __other.__isVisible(PropId.byIndex(SLOT_CONTENT))) {
                    return false;
                }
                boolean __contentLoaded = __contentValue != null;
                if (__contentLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CONTENT))) {
                    return false;
                }
                if (__contentLoaded && !Objects.equals(__contentValue, __other.content())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_LIKES)) != __other.__isVisible(PropId.byIndex(SLOT_LIKES))) {
                    return false;
                }
                boolean __likesLoaded = __likesValue != null;
                if (__likesLoaded != __other.__isLoaded(PropId.byIndex(SLOT_LIKES))) {
                    return false;
                }
                if (__likesLoaded && !Objects.equals(__likesValue, __other.likes())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USER)) != __other.__isVisible(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                boolean __userLoaded = __userValue != null;
                if (__userLoaded != __other.__isLoaded(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                if (__userLoaded && !Objects.equals(__userValue, __other.user())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_VIDEO)) != __other.__isVisible(PropId.byIndex(SLOT_VIDEO))) {
                    return false;
                }
                boolean __videoLoaded = __videoValue != null;
                if (__videoLoaded != __other.__isLoaded(PropId.byIndex(SLOT_VIDEO))) {
                    return false;
                }
                if (__videoLoaded && !Objects.equals(__videoValue, __other.video())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_ROOT)) != __other.__isVisible(PropId.byIndex(SLOT_ROOT))) {
                    return false;
                }
                boolean __rootLoaded = this.__rootLoaded;
                if (__rootLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ROOT))) {
                    return false;
                }
                if (__rootLoaded && !Objects.equals(__rootValue, __other.root())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_PARENT)) != __other.__isVisible(PropId.byIndex(SLOT_PARENT))) {
                    return false;
                }
                boolean __parentLoaded = this.__parentLoaded;
                if (__parentLoaded != __other.__isLoaded(PropId.byIndex(SLOT_PARENT))) {
                    return false;
                }
                if (__parentLoaded && !Objects.equals(__parentValue, __other.parent())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_CHILDREN)) != __other.__isVisible(PropId.byIndex(SLOT_CHILDREN))) {
                    return false;
                }
                boolean __childrenLoaded = __childrenValue != null;
                if (__childrenLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CHILDREN))) {
                    return false;
                }
                if (__childrenLoaded && !Objects.equals(__childrenValue, __other.children())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_LIKES_SUM)) != __other.__isVisible(PropId.byIndex(SLOT_LIKES_SUM))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_CHILDREN_SUM)) != __other.__isVisible(PropId.byIndex(SLOT_CHILDREN_SUM))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USER_IS_LIKE)) != __other.__isVisible(PropId.byIndex(SLOT_USER_IS_LIKE))) {
                    return false;
                }
                return true;
            }

            private boolean __shallowEquals(Object obj) {
                if (obj == null || !(obj instanceof Implementor)) {
                    return false;
                }
                Implementor __other = (Implementor)obj;
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                boolean __idLoaded = this.__idLoaded;
                if (__idLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                if (__idLoaded && __idValue != __other.id()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATE_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                boolean __createTimeLoaded = __createTimeValue != null;
                if (__createTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                if (__createTimeLoaded && __createTimeValue != __other.createTime()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_MODIFY_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                boolean __modifyTimeLoaded = this.__modifyTimeLoaded;
                if (__modifyTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                if (__modifyTimeLoaded && __modifyTimeValue != __other.modifyTime()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_CONTENT)) != __other.__isVisible(PropId.byIndex(SLOT_CONTENT))) {
                    return false;
                }
                boolean __contentLoaded = __contentValue != null;
                if (__contentLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CONTENT))) {
                    return false;
                }
                if (__contentLoaded && __contentValue != __other.content()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_LIKES)) != __other.__isVisible(PropId.byIndex(SLOT_LIKES))) {
                    return false;
                }
                boolean __likesLoaded = __likesValue != null;
                if (__likesLoaded != __other.__isLoaded(PropId.byIndex(SLOT_LIKES))) {
                    return false;
                }
                if (__likesLoaded && __likesValue != __other.likes()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USER)) != __other.__isVisible(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                boolean __userLoaded = __userValue != null;
                if (__userLoaded != __other.__isLoaded(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                if (__userLoaded && __userValue != __other.user()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_VIDEO)) != __other.__isVisible(PropId.byIndex(SLOT_VIDEO))) {
                    return false;
                }
                boolean __videoLoaded = __videoValue != null;
                if (__videoLoaded != __other.__isLoaded(PropId.byIndex(SLOT_VIDEO))) {
                    return false;
                }
                if (__videoLoaded && __videoValue != __other.video()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_ROOT)) != __other.__isVisible(PropId.byIndex(SLOT_ROOT))) {
                    return false;
                }
                boolean __rootLoaded = this.__rootLoaded;
                if (__rootLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ROOT))) {
                    return false;
                }
                if (__rootLoaded && __rootValue != __other.root()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_PARENT)) != __other.__isVisible(PropId.byIndex(SLOT_PARENT))) {
                    return false;
                }
                boolean __parentLoaded = this.__parentLoaded;
                if (__parentLoaded != __other.__isLoaded(PropId.byIndex(SLOT_PARENT))) {
                    return false;
                }
                if (__parentLoaded && __parentValue != __other.parent()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_CHILDREN)) != __other.__isVisible(PropId.byIndex(SLOT_CHILDREN))) {
                    return false;
                }
                boolean __childrenLoaded = __childrenValue != null;
                if (__childrenLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CHILDREN))) {
                    return false;
                }
                if (__childrenLoaded && __childrenValue != __other.children()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_LIKES_SUM)) != __other.__isVisible(PropId.byIndex(SLOT_LIKES_SUM))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_CHILDREN_SUM)) != __other.__isVisible(PropId.byIndex(SLOT_CHILDREN_SUM))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USER_IS_LIKE)) != __other.__isVisible(PropId.byIndex(SLOT_USER_IS_LIKE))) {
                    return false;
                }
                return true;
            }

            @Override
            public boolean __equals(Object obj, boolean shallow) {
                return shallow ? __shallowEquals(obj) : equals(obj);
            }

            @Override
            public String toString() {
                return ImmutableObjects.toString(this);
            }
        }

        @GeneratedBy(
                type = Comment.class
        )
        private static class DraftImpl implements Implementor, DraftSpi, CommentDraft {
            private DraftContext __ctx;

            private Impl __base;

            private Impl __modified;

            private boolean __resolving;

            private Comment __resolved;

            DraftImpl(DraftContext ctx, Comment base) {
                __ctx = ctx;
                if (base != null) {
                    __base = (Impl)base;
                }
                else {
                    __modified = new Impl();
                }
            }

            @Override
            public boolean __isLoaded(PropId prop) {
                return (__modified!= null ? __modified : __base).__isLoaded(prop);
            }

            @Override
            public boolean __isLoaded(String prop) {
                return (__modified!= null ? __modified : __base).__isLoaded(prop);
            }

            @Override
            public boolean __isVisible(PropId prop) {
                return (__modified!= null ? __modified : __base).__isVisible(prop);
            }

            @Override
            public boolean __isVisible(String prop) {
                return (__modified!= null ? __modified : __base).__isVisible(prop);
            }

            @Override
            public int hashCode() {
                return (__modified!= null ? __modified : __base).hashCode();
            }

            @Override
            public int __hashCode(boolean shallow) {
                return (__modified!= null ? __modified : __base).__hashCode(shallow);
            }

            @Override
            public boolean equals(Object obj) {
                return (__modified!= null ? __modified : __base).equals(obj);
            }

            @Override
            public boolean __equals(Object obj, boolean shallow) {
                return (__modified!= null ? __modified : __base).__equals(obj, shallow);
            }

            @Override
            public String toString() {
                return ImmutableObjects.toString(this);
            }

            @Override
            @JsonIgnore
            public long id() {
                return (__modified!= null ? __modified : __base).id();
            }

            @Override
            public CommentDraft setId(long id) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__idValue = id;
                __tmpModified.__idLoaded = true;
                return this;
            }

            @Override
            @JsonIgnore
            public Date createTime() {
                return (__modified!= null ? __modified : __base).createTime();
            }

            @Override
            public CommentDraft setCreateTime(Date createTime) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (createTime == null) {
                    throw new IllegalArgumentException(
                        "'createTime' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__createTimeValue = createTime;
                return this;
            }

            @Override
            @JsonIgnore
            @Nullable
            public Date modifyTime() {
                return (__modified!= null ? __modified : __base).modifyTime();
            }

            @Override
            public CommentDraft setModifyTime(Date modifyTime) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__modifyTimeValue = modifyTime;
                __tmpModified.__modifyTimeLoaded = true;
                return this;
            }

            @Override
            @JsonIgnore
            public String betweenTime() {
                return (__modified!= null ? __modified : __base).betweenTime();
            }

            @Override
            @JsonIgnore
            public String content() {
                return (__modified!= null ? __modified : __base).content();
            }

            @Override
            public CommentDraft setContent(String content) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (content == null) {
                    throw new IllegalArgumentException(
                        "'content' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__contentValue = content;
                return this;
            }

            @Override
            @JsonIgnore
            public List<CommentLike> likes() {
                return __ctx.toDraftList((__modified!= null ? __modified : __base).likes(), CommentLike.class, true);
            }

            @Override
            public List<CommentLikeDraft> likes(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_LIKES)))) {
                    setLikes(new ArrayList<>());
                }
                return __ctx.toDraftList((__modified!= null ? __modified : __base).likes(), CommentLike.class, true);
            }

            @Override
            public CommentDraft setLikes(List<CommentLike> likes) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (likes == null) {
                    throw new IllegalArgumentException(
                        "'likes' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__likesValue = NonSharedList.of(__tmpModified.__likesValue, likes);
                return this;
            }

            @Override
            public CommentDraft addIntoLikes(DraftConsumer<CommentLikeDraft> block) {
                addIntoLikes(null, block);
                return this;
            }

            @Override
            public CommentDraft addIntoLikes(CommentLike base,
                    DraftConsumer<CommentLikeDraft> block) {
                likes(true).add((CommentLikeDraft)CommentLikeDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public UserDraft user() {
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).user());
            }

            @Override
            public UserDraft user(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_USER)))) {
                    setUser(UserDraft.$.produce(null, null));
                }
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).user());
            }

            @Override
            public CommentDraft setUser(User user) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (user == null) {
                    throw new IllegalArgumentException(
                        "'user' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__userValue = user;
                return this;
            }

            @Override
            public long userId() {
                return user().id();
            }

            @OldChain
            @Override
            public CommentDraft setUserId(long userId) {
                user(true).setId(Objects.requireNonNull(userId, "\"user\" cannot be null"));
                return this;
            }

            @Override
            public CommentDraft applyUser(DraftConsumer<UserDraft> block) {
                applyUser(null, block);
                return this;
            }

            @Override
            public CommentDraft applyUser(User base, DraftConsumer<UserDraft> block) {
                setUser(UserDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public VideoDraft video() {
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).video());
            }

            @Override
            public VideoDraft video(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_VIDEO)))) {
                    setVideo(VideoDraft.$.produce(null, null));
                }
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).video());
            }

            @Override
            public CommentDraft setVideo(Video video) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (video == null) {
                    throw new IllegalArgumentException(
                        "'video' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__videoValue = video;
                return this;
            }

            @Override
            public long videoId() {
                return video().id();
            }

            @OldChain
            @Override
            public CommentDraft setVideoId(long videoId) {
                video(true).setId(Objects.requireNonNull(videoId, "\"video\" cannot be null"));
                return this;
            }

            @Override
            public CommentDraft applyVideo(DraftConsumer<VideoDraft> block) {
                applyVideo(null, block);
                return this;
            }

            @Override
            public CommentDraft applyVideo(Video base, DraftConsumer<VideoDraft> block) {
                setVideo(VideoDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            @Nullable
            public CommentDraft root() {
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).root());
            }

            @Override
            public CommentDraft root(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_ROOT)) || root() == null)) {
                    setRoot(CommentDraft.$.produce(null, null));
                }
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).root());
            }

            @Override
            public CommentDraft setRoot(Comment root) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__rootValue = root;
                __tmpModified.__rootLoaded = true;
                return this;
            }

            @Nullable
            @Override
            public Long rootId() {
                Comment root = root();
                if (root == null) {
                    return null;
                }
                return root.id();
            }

            @OldChain
            @Override
            public CommentDraft setRootId(@Nullable Long rootId) {
                if (rootId == null) {
                    setRoot(null);
                    return this;
                }
                root(true).setId(rootId);
                return this;
            }

            @Override
            public CommentDraft applyRoot(DraftConsumer<CommentDraft> block) {
                applyRoot(null, block);
                return this;
            }

            @Override
            public CommentDraft applyRoot(Comment base, DraftConsumer<CommentDraft> block) {
                setRoot(CommentDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            @Nullable
            public CommentDraft parent() {
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).parent());
            }

            @Override
            public CommentDraft parent(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_PARENT)) || parent() == null)) {
                    setParent(CommentDraft.$.produce(null, null));
                }
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).parent());
            }

            @Override
            public CommentDraft setParent(Comment parent) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__parentValue = parent;
                __tmpModified.__parentLoaded = true;
                return this;
            }

            @Nullable
            @Override
            public Long parentId() {
                Comment parent = parent();
                if (parent == null) {
                    return null;
                }
                return parent.id();
            }

            @OldChain
            @Override
            public CommentDraft setParentId(@Nullable Long parentId) {
                if (parentId == null) {
                    setParent(null);
                    return this;
                }
                parent(true).setId(parentId);
                return this;
            }

            @Override
            public CommentDraft applyParent(DraftConsumer<CommentDraft> block) {
                applyParent(null, block);
                return this;
            }

            @Override
            public CommentDraft applyParent(Comment base, DraftConsumer<CommentDraft> block) {
                setParent(CommentDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public List<Comment> children() {
                return __ctx.toDraftList((__modified!= null ? __modified : __base).children(), Comment.class, true);
            }

            @Override
            public List<CommentDraft> children(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_CHILDREN)))) {
                    setChildren(new ArrayList<>());
                }
                return __ctx.toDraftList((__modified!= null ? __modified : __base).children(), Comment.class, true);
            }

            @Override
            public CommentDraft setChildren(List<Comment> children) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (children == null) {
                    throw new IllegalArgumentException(
                        "'children' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__childrenValue = NonSharedList.of(__tmpModified.__childrenValue, children);
                return this;
            }

            @Override
            public CommentDraft addIntoChildren(DraftConsumer<CommentDraft> block) {
                addIntoChildren(null, block);
                return this;
            }

            @Override
            public CommentDraft addIntoChildren(Comment base, DraftConsumer<CommentDraft> block) {
                children(true).add((CommentDraft)CommentDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public long likesSum() {
                return (__modified!= null ? __modified : __base).likesSum();
            }

            @Override
            @JsonIgnore
            public long childrenSum() {
                return (__modified!= null ? __modified : __base).childrenSum();
            }

            @Override
            @JsonIgnore
            public boolean userIsLike() {
                return (__modified!= null ? __modified : __base).userIsLike();
            }

            @SuppressWarnings("unchecked")
            @Override
            public void __set(PropId prop, Object value) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__set(prop.asName(), value);
                    return;
                    case SLOT_CREATE_TIME:
                    		setCreateTime((Date)value);break;
                    case SLOT_MODIFY_TIME:
                    		setModifyTime((Date)value);break;
                    case SLOT_BETWEEN_TIME:
                    		break;
                    case SLOT_ID:
                    		if (value == null) throw new IllegalArgumentException("'id' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setId((Long)value);
                            break;
                    case SLOT_CONTENT:
                    		setContent((String)value);break;
                    case SLOT_LIKES:
                    		setLikes((List<CommentLike>)value);break;
                    case SLOT_USER:
                    		setUser((User)value);break;
                    case SLOT_VIDEO:
                    		setVideo((Video)value);break;
                    case SLOT_ROOT:
                    		setRoot((Comment)value);break;
                    case SLOT_PARENT:
                    		setParent((Comment)value);break;
                    case SLOT_CHILDREN:
                    		setChildren((List<Comment>)value);break;
                    case SLOT_LIKES_SUM:
                    		break;
                    case SLOT_CHILDREN_SUM:
                    		break;
                    case SLOT_USER_IS_LIKE:
                    		break;
                    default: throw new IllegalArgumentException("Illegal property id for \"com.celeste.entity.Comment\": \"" + prop + "\"");
                }
            }

            @SuppressWarnings("unchecked")
            @Override
            public void __set(String prop, Object value) {
                switch (prop) {
                    case "createTime":
                    		setCreateTime((Date)value);break;
                    case "modifyTime":
                    		setModifyTime((Date)value);break;
                    case "betweenTime":
                    		break;
                    case "id":
                    		if (value == null) throw new IllegalArgumentException("'id' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setId((Long)value);
                            break;
                    case "content":
                    		setContent((String)value);break;
                    case "likes":
                    		setLikes((List<CommentLike>)value);break;
                    case "user":
                    		setUser((User)value);break;
                    case "video":
                    		setVideo((Video)value);break;
                    case "root":
                    		setRoot((Comment)value);break;
                    case "parent":
                    		setParent((Comment)value);break;
                    case "children":
                    		setChildren((List<Comment>)value);break;
                    case "likesSum":
                    		break;
                    case "childrenSum":
                    		break;
                    case "userIsLike":
                    		break;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Comment\": \"" + prop + "\"");
                }
            }

            @Override
            public void __show(PropId prop, boolean visible) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Visibility __visibility = (__modified!= null ? __modified : __base).__visibility;
                if (__visibility == null) {
                    if (visible) {
                        return;
                    }
                    __modified().__visibility = __visibility = Visibility.of(14);
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__show(prop.asName(), visible);
                    return;
                    case SLOT_CREATE_TIME:
                    		__visibility.show(SLOT_CREATE_TIME, visible);break;
                    case SLOT_MODIFY_TIME:
                    		__visibility.show(SLOT_MODIFY_TIME, visible);break;
                    case SLOT_BETWEEN_TIME:
                    		__visibility.show(SLOT_BETWEEN_TIME, visible);break;
                    case SLOT_ID:
                    		__visibility.show(SLOT_ID, visible);break;
                    case SLOT_CONTENT:
                    		__visibility.show(SLOT_CONTENT, visible);break;
                    case SLOT_LIKES:
                    		__visibility.show(SLOT_LIKES, visible);break;
                    case SLOT_USER:
                    		__visibility.show(SLOT_USER, visible);break;
                    case SLOT_VIDEO:
                    		__visibility.show(SLOT_VIDEO, visible);break;
                    case SLOT_ROOT:
                    		__visibility.show(SLOT_ROOT, visible);break;
                    case SLOT_PARENT:
                    		__visibility.show(SLOT_PARENT, visible);break;
                    case SLOT_CHILDREN:
                    		__visibility.show(SLOT_CHILDREN, visible);break;
                    case SLOT_LIKES_SUM:
                    		__visibility.show(SLOT_LIKES_SUM, visible);break;
                    case SLOT_CHILDREN_SUM:
                    		__visibility.show(SLOT_CHILDREN_SUM, visible);break;
                    case SLOT_USER_IS_LIKE:
                    		__visibility.show(SLOT_USER_IS_LIKE, visible);break;
                    default: throw new IllegalArgumentException(
                                "Illegal property id for \"com.celeste.entity.Comment\": \"" + 
                                prop + 
                                "\",it does not exists"
                            );
                }
            }

            @Override
            public void __show(String prop, boolean visible) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Visibility __visibility = (__modified!= null ? __modified : __base).__visibility;
                if (__visibility == null) {
                    if (visible) {
                        return;
                    }
                    __modified().__visibility = __visibility = Visibility.of(14);
                }
                switch (prop) {
                    case "createTime":
                    		__visibility.show(SLOT_CREATE_TIME, visible);break;
                    case "modifyTime":
                    		__visibility.show(SLOT_MODIFY_TIME, visible);break;
                    case "betweenTime":
                    		__visibility.show(SLOT_BETWEEN_TIME, visible);break;
                    case "id":
                    		__visibility.show(SLOT_ID, visible);break;
                    case "content":
                    		__visibility.show(SLOT_CONTENT, visible);break;
                    case "likes":
                    		__visibility.show(SLOT_LIKES, visible);break;
                    case "user":
                    		__visibility.show(SLOT_USER, visible);break;
                    case "video":
                    		__visibility.show(SLOT_VIDEO, visible);break;
                    case "root":
                    		__visibility.show(SLOT_ROOT, visible);break;
                    case "parent":
                    		__visibility.show(SLOT_PARENT, visible);break;
                    case "children":
                    		__visibility.show(SLOT_CHILDREN, visible);break;
                    case "likesSum":
                    		__visibility.show(SLOT_LIKES_SUM, visible);break;
                    case "childrenSum":
                    		__visibility.show(SLOT_CHILDREN_SUM, visible);break;
                    case "userIsLike":
                    		__visibility.show(SLOT_USER_IS_LIKE, visible);break;
                    default: throw new IllegalArgumentException(
                                "Illegal property name for \"com.celeste.entity.Comment\": \"" + 
                                prop + 
                                "\",it does not exists"
                            );
                }
            }

            @Override
            public void __unload(PropId prop) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__unload(prop.asName());
                    return;
                    case SLOT_CREATE_TIME:
                    		__modified().__createTimeValue = null;break;
                    case SLOT_MODIFY_TIME:
                    		__modified().__modifyTimeLoaded = false;break;
                    case SLOT_BETWEEN_TIME:
                    		break;
                    case SLOT_ID:
                    		__modified().__idLoaded = false;break;
                    case SLOT_CONTENT:
                    		__modified().__contentValue = null;break;
                    case SLOT_LIKES:
                    		__modified().__likesValue = null;break;
                    case SLOT_USER:
                    		__modified().__userValue = null;break;
                    case SLOT_VIDEO:
                    		__modified().__videoValue = null;break;
                    case SLOT_ROOT:
                    		__modified().__rootLoaded = false;break;
                    case SLOT_PARENT:
                    		__modified().__parentLoaded = false;break;
                    case SLOT_CHILDREN:
                    		__modified().__childrenValue = null;break;
                    case SLOT_LIKES_SUM:
                    		break;
                    case SLOT_CHILDREN_SUM:
                    		break;
                    case SLOT_USER_IS_LIKE:
                    		break;
                    default: throw new IllegalArgumentException("Illegal property id for \"com.celeste.entity.Comment\": \"" + prop + "\", it does not exist or its loaded state is not controllable");
                }
            }

            @Override
            public void __unload(String prop) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                switch (prop) {
                    case "createTime":
                    		__modified().__createTimeValue = null;break;
                    case "modifyTime":
                    		__modified().__modifyTimeLoaded = false;break;
                    case "betweenTime":
                    		break;
                    case "id":
                    		__modified().__idLoaded = false;break;
                    case "content":
                    		__modified().__contentValue = null;break;
                    case "likes":
                    		__modified().__likesValue = null;break;
                    case "user":
                    		__modified().__userValue = null;break;
                    case "video":
                    		__modified().__videoValue = null;break;
                    case "root":
                    		__modified().__rootLoaded = false;break;
                    case "parent":
                    		__modified().__parentLoaded = false;break;
                    case "children":
                    		__modified().__childrenValue = null;break;
                    case "likesSum":
                    		break;
                    case "childrenSum":
                    		break;
                    case "userIsLike":
                    		break;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Comment\": \"" + prop + "\", it does not exist or its loaded state is not controllable");
                }
            }

            @Override
            public DraftContext __draftContext() {
                return __ctx;
            }

            @Override
            public Object __resolve() {
                if (__resolved != null) {
                    return __resolved;
                }
                if (__resolving) {
                    throw new CircularReferenceException();
                }
                __resolving = true;
                try {
                    Implementor base = __base;
                    Impl __tmpModified = __modified;
                    if (__tmpModified == null) {
                        if (base.__isLoaded(PropId.byIndex(SLOT_LIKES))) {
                            List<CommentLike> oldValue = base.likes();
                            List<CommentLike> newValue = __ctx.resolveList(oldValue);
                            if (oldValue != newValue) {
                                setLikes(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_USER))) {
                            User oldValue = base.user();
                            User newValue = __ctx.resolveObject(oldValue);
                            if (oldValue != newValue) {
                                setUser(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_VIDEO))) {
                            Video oldValue = base.video();
                            Video newValue = __ctx.resolveObject(oldValue);
                            if (oldValue != newValue) {
                                setVideo(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_ROOT))) {
                            Comment oldValue = base.root();
                            Comment newValue = __ctx.resolveObject(oldValue);
                            if (oldValue != newValue) {
                                setRoot(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_PARENT))) {
                            Comment oldValue = base.parent();
                            Comment newValue = __ctx.resolveObject(oldValue);
                            if (oldValue != newValue) {
                                setParent(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_CHILDREN))) {
                            List<Comment> oldValue = base.children();
                            List<Comment> newValue = __ctx.resolveList(oldValue);
                            if (oldValue != newValue) {
                                setChildren(newValue);
                            }
                        }
                        __tmpModified = __modified;
                    }
                    else {
                        __tmpModified.__likesValue = NonSharedList.of(__tmpModified.__likesValue, __ctx.resolveList(__tmpModified.__likesValue));
                        __tmpModified.__userValue = __ctx.resolveObject(__tmpModified.__userValue);
                        __tmpModified.__videoValue = __ctx.resolveObject(__tmpModified.__videoValue);
                        __tmpModified.__rootValue = __ctx.resolveObject(__tmpModified.__rootValue);
                        __tmpModified.__parentValue = __ctx.resolveObject(__tmpModified.__parentValue);
                        __tmpModified.__childrenValue = NonSharedList.of(__tmpModified.__childrenValue, __ctx.resolveList(__tmpModified.__childrenValue));
                    }
                    if (__base != null && __tmpModified == null) {
                        this.__resolved = base;
                        return base;
                    }
                    this.__resolved = __tmpModified;
                    return __tmpModified;
                }
                finally {
                    __resolving = false;
                }
            }

            @Override
            public boolean __isResolved() {
                return __resolved != null;
            }

            Impl __modified() {
                Impl __tmpModified = __modified;
                if (__tmpModified == null) {
                    __tmpModified = __base.clone();
                    __modified = __tmpModified;
                }
                return __tmpModified;
            }
        }
    }

    @GeneratedBy(
            type = Comment.class
    )
    class Builder {
        private final Producer.DraftImpl __draft;

        public Builder() {
            __draft = new Producer.DraftImpl(null, null);
            __draft.__show(PropId.byIndex(Producer.SLOT_CREATE_TIME), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_BETWEEN_TIME), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_LIKES), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_CHILDREN), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_LIKES_SUM), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_CHILDREN_SUM), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_USER_IS_LIKE), false);
        }

        public Builder id(Long id) {
            if (id != null) {
                __draft.setId(id);
            }
            return this;
        }

        public Builder createTime(Date createTime) {
            if (createTime != null) {
                __draft.setCreateTime(createTime);
                __draft.__show(PropId.byIndex(Producer.SLOT_CREATE_TIME), true);
            }
            return this;
        }

        @Nullable
        public Builder modifyTime(Date modifyTime) {
            __draft.setModifyTime(modifyTime);
            return this;
        }

        public Builder content(String content) {
            if (content != null) {
                __draft.setContent(content);
            }
            return this;
        }

        public Builder likes(List<CommentLike> likes) {
            if (likes != null) {
                __draft.setLikes(likes);
                __draft.__show(PropId.byIndex(Producer.SLOT_LIKES), true);
            }
            return this;
        }

        public Builder user(User user) {
            if (user != null) {
                __draft.setUser(user);
            }
            return this;
        }

        public Builder video(Video video) {
            if (video != null) {
                __draft.setVideo(video);
            }
            return this;
        }

        @Nullable
        public Builder root(Comment root) {
            __draft.setRoot(root);
            return this;
        }

        @Nullable
        public Builder parent(Comment parent) {
            __draft.setParent(parent);
            return this;
        }

        public Builder children(List<Comment> children) {
            if (children != null) {
                __draft.setChildren(children);
                __draft.__show(PropId.byIndex(Producer.SLOT_CHILDREN), true);
            }
            return this;
        }

        public Comment build() {
            return (Comment)__draft.__modified();
        }
    }
}
