package com.celeste.entity;

import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.sql.JoinType;
import org.babyfish.jimmer.sql.ast.impl.table.TableImplementor;
import org.babyfish.jimmer.sql.ast.impl.table.TableProxies;
import org.babyfish.jimmer.sql.ast.table.Table;
import org.babyfish.jimmer.sql.ast.table.TableEx;
import org.babyfish.jimmer.sql.ast.table.WeakJoin;
import org.babyfish.jimmer.sql.ast.table.spi.AbstractTypedTable;

@GeneratedBy(
        type = VideoLike.class
)
public class VideoLikeTableEx extends VideoLikeTable implements TableEx<VideoLike> {
    public static final VideoLikeTableEx $ = new VideoLikeTableEx(VideoLikeTable.$, null);

    public VideoLikeTableEx() {
        super();
    }

    public VideoLikeTableEx(AbstractTypedTable.DelayedOperation<VideoLike> delayedOperation) {
        super(delayedOperation);
    }

    public VideoLikeTableEx(TableImplementor<VideoLike> table) {
        super(table);
    }

    protected VideoLikeTableEx(VideoLikeTable base, String joinDisabledReason) {
        super(base, joinDisabledReason);
    }

    public UserTableEx user() {
        __beforeJoin();
        if (raw != null) {
            return new UserTableEx(raw.joinImplementor(VideoLikeProps.USER.unwrap()));
        }
        return new UserTableEx(joinOperation(VideoLikeProps.USER.unwrap()));
    }

    public UserTableEx user(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new UserTableEx(raw.joinImplementor(VideoLikeProps.USER.unwrap(), joinType));
        }
        return new UserTableEx(joinOperation(VideoLikeProps.USER.unwrap(), joinType));
    }

    public VideoTableEx video() {
        __beforeJoin();
        if (raw != null) {
            return new VideoTableEx(raw.joinImplementor(VideoLikeProps.VIDEO.unwrap()));
        }
        return new VideoTableEx(joinOperation(VideoLikeProps.VIDEO.unwrap()));
    }

    public VideoTableEx video(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new VideoTableEx(raw.joinImplementor(VideoLikeProps.VIDEO.unwrap(), joinType));
        }
        return new VideoTableEx(joinOperation(VideoLikeProps.VIDEO.unwrap(), joinType));
    }

    @Override
    public VideoLikeTableEx asTableEx() {
        return this;
    }

    @Override
    public VideoLikeTableEx __disableJoin(String reason) {
        return new VideoLikeTableEx(this, reason);
    }

    public <TT extends Table<?>, WJ extends WeakJoin<VideoLikeTable, TT>> TT weakJoin(
            Class<WJ> weakJoinType) {
        return weakJoin(weakJoinType, JoinType.INNER);
    }

    @SuppressWarnings("unchecked")
    public <TT extends Table<?>, WJ extends WeakJoin<VideoLikeTable, TT>> TT weakJoin(
            Class<WJ> weakJoinType, JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return (TT)TableProxies.wrap(raw.weakJoinImplementor(weakJoinType, joinType));
        }
        return (TT)TableProxies.fluent(joinOperation(weakJoinType, joinType));
    }
}
