package com.celeste.entity;

import java.lang.Long;
import java.lang.String;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.meta.ImmutableType;
import org.babyfish.jimmer.meta.TypedProp;
import org.babyfish.jimmer.sql.ast.PropExpression;
import org.babyfish.jimmer.sql.ast.table.Props;
import org.babyfish.jimmer.sql.ast.table.PropsFor;

@GeneratedBy(
        type = Authority.class
)
@PropsFor(Authority.class)
public interface AuthorityProps extends Props {
    TypedProp.Scalar<Authority, Long> ID = 
        TypedProp.scalar(ImmutableType.get(Authority.class).getProp("id"));

    TypedProp.Scalar<Authority, String> NAME = 
        TypedProp.scalar(ImmutableType.get(Authority.class).getProp("name"));

    PropExpression.Num<Long> id();

    PropExpression.Str name();
}
