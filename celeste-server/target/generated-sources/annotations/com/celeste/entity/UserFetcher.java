package com.celeste.entity;

import java.lang.Override;
import java.util.function.Consumer;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.lang.NewChain;
import org.babyfish.jimmer.meta.ImmutableProp;
import org.babyfish.jimmer.sql.ast.table.Table;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.babyfish.jimmer.sql.fetcher.FieldConfig;
import org.babyfish.jimmer.sql.fetcher.IdOnlyFetchType;
import org.babyfish.jimmer.sql.fetcher.ListFieldConfig;
import org.babyfish.jimmer.sql.fetcher.RecursiveListFieldConfig;
import org.babyfish.jimmer.sql.fetcher.impl.FetcherImpl;
import org.babyfish.jimmer.sql.fetcher.spi.AbstractTypedFetcher;

@GeneratedBy(
        type = User.class
)
public class UserFetcher extends AbstractTypedFetcher<User, UserFetcher> {
    public static final UserFetcher $ = new UserFetcher(null);

    private UserFetcher(FetcherImpl<User> base) {
        super(User.class, base);
    }

    private UserFetcher(UserFetcher prev, ImmutableProp prop, boolean negative,
            IdOnlyFetchType idOnlyFetchType) {
        super(prev, prop, negative, idOnlyFetchType);
    }

    private UserFetcher(UserFetcher prev, ImmutableProp prop,
            FieldConfig<?, ? extends Table<?>> fieldConfig) {
        super(prev, prop, fieldConfig);
    }

    public static UserFetcher $from(Fetcher<User> base) {
        return base instanceof UserFetcher ? 
        	(UserFetcher)base : 
        	new UserFetcher((FetcherImpl<User>)base);
    }

    @NewChain
    public UserFetcher createTime() {
        return add("createTime");
    }

    @NewChain
    public UserFetcher createTime(boolean enabled) {
        return enabled ? add("createTime") : remove("createTime");
    }

    @NewChain
    public UserFetcher modifyTime() {
        return add("modifyTime");
    }

    @NewChain
    public UserFetcher modifyTime(boolean enabled) {
        return enabled ? add("modifyTime") : remove("modifyTime");
    }

    @NewChain
    public UserFetcher betweenTime() {
        return add("betweenTime");
    }

    @NewChain
    public UserFetcher betweenTime(boolean enabled) {
        return enabled ? add("betweenTime") : remove("betweenTime");
    }

    @NewChain
    public UserFetcher username() {
        return add("username");
    }

    @NewChain
    public UserFetcher username(boolean enabled) {
        return enabled ? add("username") : remove("username");
    }

    @NewChain
    public UserFetcher password() {
        return add("password");
    }

    @NewChain
    public UserFetcher password(boolean enabled) {
        return enabled ? add("password") : remove("password");
    }

    @NewChain
    public UserFetcher email() {
        return add("email");
    }

    @NewChain
    public UserFetcher email(boolean enabled) {
        return enabled ? add("email") : remove("email");
    }

    @NewChain
    public UserFetcher description() {
        return add("description");
    }

    @NewChain
    public UserFetcher description(boolean enabled) {
        return enabled ? add("description") : remove("description");
    }

    @NewChain
    public UserFetcher gender() {
        return add("gender");
    }

    @NewChain
    public UserFetcher gender(boolean enabled) {
        return enabled ? add("gender") : remove("gender");
    }

    @NewChain
    public UserFetcher home() {
        return add("home");
    }

    @NewChain
    public UserFetcher home(boolean enabled) {
        return enabled ? add("home") : remove("home");
    }

    @NewChain
    public UserFetcher status() {
        return add("status");
    }

    @NewChain
    public UserFetcher status(boolean enabled) {
        return enabled ? add("status") : remove("status");
    }

    @NewChain
    public UserFetcher types() {
        return add("types");
    }

    @NewChain
    public UserFetcher types(boolean enabled) {
        return enabled ? add("types") : remove("types");
    }

    @NewChain
    public UserFetcher types(Fetcher<Type> childFetcher) {
        return add("types", childFetcher);
    }

    @NewChain
    public UserFetcher types(Fetcher<Type> childFetcher,
            Consumer<ListFieldConfig<Type, TypeTable>> fieldConfig) {
        return add("types", childFetcher, fieldConfig);
    }

    @NewChain
    public UserFetcher types(IdOnlyFetchType idOnlyFetchType) {
        return add("types", idOnlyFetchType);
    }

    @NewChain
    public UserFetcher adores() {
        return add("adores");
    }

    @NewChain
    public UserFetcher adores(boolean enabled) {
        return enabled ? add("adores") : remove("adores");
    }

    @NewChain
    public UserFetcher adores(Fetcher<User> childFetcher) {
        return add("adores", childFetcher);
    }

    @NewChain
    public UserFetcher adores(Fetcher<User> childFetcher,
            Consumer<ListFieldConfig<User, UserTable>> fieldConfig) {
        return add("adores", childFetcher, fieldConfig);
    }

    @NewChain
    public UserFetcher recursiveAdores() {
        return addRecursion("adores", null);
    }

    @NewChain
    public UserFetcher recursiveAdores(
            Consumer<RecursiveListFieldConfig<User, UserTable>> fieldConfig) {
        return addRecursion("adores", fieldConfig);
    }

    @NewChain
    public UserFetcher adores(IdOnlyFetchType idOnlyFetchType) {
        return add("adores", idOnlyFetchType);
    }

    @NewChain
    public UserFetcher followers() {
        return add("followers");
    }

    @NewChain
    public UserFetcher followers(boolean enabled) {
        return enabled ? add("followers") : remove("followers");
    }

    @NewChain
    public UserFetcher followers(Fetcher<User> childFetcher) {
        return add("followers", childFetcher);
    }

    @NewChain
    public UserFetcher followers(Fetcher<User> childFetcher,
            Consumer<ListFieldConfig<User, UserTable>> fieldConfig) {
        return add("followers", childFetcher, fieldConfig);
    }

    @NewChain
    public UserFetcher recursiveFollowers() {
        return addRecursion("followers", null);
    }

    @NewChain
    public UserFetcher recursiveFollowers(
            Consumer<RecursiveListFieldConfig<User, UserTable>> fieldConfig) {
        return addRecursion("followers", fieldConfig);
    }

    @NewChain
    public UserFetcher followers(IdOnlyFetchType idOnlyFetchType) {
        return add("followers", idOnlyFetchType);
    }

    @NewChain
    public UserFetcher roles() {
        return add("roles");
    }

    @NewChain
    public UserFetcher roles(boolean enabled) {
        return enabled ? add("roles") : remove("roles");
    }

    @NewChain
    public UserFetcher roles(Fetcher<Role> childFetcher) {
        return add("roles", childFetcher);
    }

    @NewChain
    public UserFetcher roles(Fetcher<Role> childFetcher,
            Consumer<ListFieldConfig<Role, RoleTable>> fieldConfig) {
        return add("roles", childFetcher, fieldConfig);
    }

    @NewChain
    public UserFetcher roles(IdOnlyFetchType idOnlyFetchType) {
        return add("roles", idOnlyFetchType);
    }

    @NewChain
    public UserFetcher videos() {
        return add("videos");
    }

    @NewChain
    public UserFetcher videos(boolean enabled) {
        return enabled ? add("videos") : remove("videos");
    }

    @NewChain
    public UserFetcher videos(Fetcher<Video> childFetcher) {
        return add("videos", childFetcher);
    }

    @NewChain
    public UserFetcher videos(Fetcher<Video> childFetcher,
            Consumer<ListFieldConfig<Video, VideoTable>> fieldConfig) {
        return add("videos", childFetcher, fieldConfig);
    }

    @NewChain
    public UserFetcher collections() {
        return add("collections");
    }

    @NewChain
    public UserFetcher collections(boolean enabled) {
        return enabled ? add("collections") : remove("collections");
    }

    @NewChain
    public UserFetcher collections(Fetcher<Collection> childFetcher) {
        return add("collections", childFetcher);
    }

    @NewChain
    public UserFetcher collections(Fetcher<Collection> childFetcher,
            Consumer<ListFieldConfig<Collection, CollectionTable>> fieldConfig) {
        return add("collections", childFetcher, fieldConfig);
    }

    @NewChain
    public UserFetcher typeIds() {
        return add("typeIds");
    }

    @NewChain
    public UserFetcher typeIds(boolean enabled) {
        return enabled ? add("typeIds") : remove("typeIds");
    }

    @NewChain
    public UserFetcher typeIds(IdOnlyFetchType idOnlyFetchType) {
        return add("typeIds", idOnlyFetchType);
    }

    @NewChain
    public UserFetcher collectedVideos() {
        return add("collectedVideos");
    }

    @NewChain
    public UserFetcher collectedVideos(boolean enabled) {
        return enabled ? add("collectedVideos") : remove("collectedVideos");
    }

    @NewChain
    public UserFetcher adoresSum() {
        return add("adoresSum");
    }

    @NewChain
    public UserFetcher adoresSum(boolean enabled) {
        return enabled ? add("adoresSum") : remove("adoresSum");
    }

    @NewChain
    public UserFetcher followersSum() {
        return add("followersSum");
    }

    @NewChain
    public UserFetcher followersSum(boolean enabled) {
        return enabled ? add("followersSum") : remove("followersSum");
    }

    @NewChain
    public UserFetcher roleList() {
        return add("roleList");
    }

    @NewChain
    public UserFetcher roleList(boolean enabled) {
        return enabled ? add("roleList") : remove("roleList");
    }

    @NewChain
    public UserFetcher authorityList() {
        return add("authorityList");
    }

    @NewChain
    public UserFetcher authorityList(boolean enabled) {
        return enabled ? add("authorityList") : remove("authorityList");
    }

    @NewChain
    public UserFetcher userIsAdore() {
        return add("userIsAdore");
    }

    @NewChain
    public UserFetcher userIsAdore(boolean enabled) {
        return enabled ? add("userIsAdore") : remove("userIsAdore");
    }

    @Override
    protected UserFetcher createFetcher(ImmutableProp prop, boolean negative,
            IdOnlyFetchType idOnlyFetchType) {
        return new UserFetcher(this, prop, negative, idOnlyFetchType);
    }

    @Override
    protected UserFetcher createFetcher(ImmutableProp prop,
            FieldConfig<?, ? extends Table<?>> fieldConfig) {
        return new UserFetcher(this, prop, fieldConfig);
    }
}
