package com.celeste.entity;

import java.lang.Deprecated;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import java.util.Date;
import java.util.function.Function;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.sql.JoinType;
import org.babyfish.jimmer.sql.ast.Predicate;
import org.babyfish.jimmer.sql.ast.PropExpression;
import org.babyfish.jimmer.sql.ast.impl.table.TableImplementor;
import org.babyfish.jimmer.sql.ast.table.TableEx;
import org.babyfish.jimmer.sql.ast.table.spi.AbstractTypedTable;

@GeneratedBy(
        type = Video.class
)
public class VideoTable extends AbstractTypedTable<Video> implements VideoProps {
    public static final VideoTable $ = new VideoTable();

    public VideoTable() {
        super(Video.class);
    }

    public VideoTable(AbstractTypedTable.DelayedOperation<Video> delayedOperation) {
        super(Video.class, delayedOperation);
    }

    public VideoTable(TableImplementor<Video> table) {
        super(table);
    }

    protected VideoTable(VideoTable base, String joinDisabledReason) {
        super(base, joinDisabledReason);
    }

    @Override
    public PropExpression.Num<Long> id() {
        return __get(VideoProps.ID.unwrap());
    }

    @Override
    public PropExpression.Cmp<Date> createTime() {
        return __get(VideoProps.CREATE_TIME.unwrap());
    }

    @Override
    public PropExpression.Cmp<Date> modifyTime() {
        return __get(VideoProps.MODIFY_TIME.unwrap());
    }

    @Override
    public PropExpression.Str description() {
        return __get(VideoProps.DESCRIPTION.unwrap());
    }

    @Override
    public PropExpression.Str status() {
        return __get(VideoProps.STATUS.unwrap());
    }

    @Override
    public TypeTable type() {
        __beforeJoin();
        if (raw != null) {
            return new TypeTable(raw.joinImplementor(VideoProps.TYPE.unwrap()));
        }
        return new TypeTable(joinOperation(VideoProps.TYPE.unwrap()));
    }

    @Override
    public TypeTable type(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new TypeTable(raw.joinImplementor(VideoProps.TYPE.unwrap(), joinType));
        }
        return new TypeTable(joinOperation(VideoProps.TYPE.unwrap(), joinType));
    }

    @Override
    public PropExpression.Num<Long> typeId() {
        return __getAssociatedId(VideoProps.TYPE.unwrap());
    }

    @Override
    public UserTable user() {
        __beforeJoin();
        if (raw != null) {
            return new UserTable(raw.joinImplementor(VideoProps.USER.unwrap()));
        }
        return new UserTable(joinOperation(VideoProps.USER.unwrap()));
    }

    @Override
    public UserTable user(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new UserTable(raw.joinImplementor(VideoProps.USER.unwrap(), joinType));
        }
        return new UserTable(joinOperation(VideoProps.USER.unwrap(), joinType));
    }

    @Override
    public PropExpression.Num<Long> userId() {
        return __getAssociatedId(VideoProps.USER.unwrap());
    }

    @Override
    public Predicate likes(Function<VideoLikeTableEx, Predicate> block) {
        return exists(VideoProps.LIKES.unwrap(), block);
    }

    @Override
    public Predicate comments(Function<CommentTableEx, Predicate> block) {
        return exists(VideoProps.COMMENTS.unwrap(), block);
    }

    @Override
    public Predicate collections(Function<CollectionTableEx, Predicate> block) {
        return exists(VideoProps.COLLECTIONS.unwrap(), block);
    }

    @Override
    public VideoTableEx asTableEx() {
        return new VideoTableEx(this, null);
    }

    @Override
    public VideoTable __disableJoin(String reason) {
        return new VideoTable(this, reason);
    }

    @GeneratedBy(
            type = Video.class
    )
    public static class Remote extends AbstractTypedTable<Video> {
        public Remote(AbstractTypedTable.DelayedOperation delayedOperation) {
            super(Video.class, delayedOperation);
        }

        public Remote(TableImplementor<Video> table) {
            super(table);
        }

        public PropExpression.Num<Long> id() {
            return __get(VideoProps.ID.unwrap());
        }

        @Override
        @Deprecated
        public TableEx<Video> asTableEx() {
            throw new UnsupportedOperationException();
        }

        @Override
        public Remote __disableJoin(String reason) {
            return this;
        }
    }
}
