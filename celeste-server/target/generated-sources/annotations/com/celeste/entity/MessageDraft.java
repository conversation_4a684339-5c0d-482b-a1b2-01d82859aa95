package com.celeste.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import java.lang.CloneNotSupportedException;
import java.lang.Cloneable;
import java.lang.IllegalArgumentException;
import java.lang.IllegalStateException;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.lang.System;
import java.util.Collections;
import java.util.Date;
import java.util.Objects;
import org.babyfish.jimmer.CircularReferenceException;
import org.babyfish.jimmer.DraftConsumer;
import org.babyfish.jimmer.ImmutableObjects;
import org.babyfish.jimmer.UnloadedException;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.jackson.ImmutableModuleRequiredException;
import org.babyfish.jimmer.lang.OldChain;
import org.babyfish.jimmer.meta.ImmutablePropCategory;
import org.babyfish.jimmer.meta.ImmutableType;
import org.babyfish.jimmer.meta.PropId;
import org.babyfish.jimmer.runtime.DraftContext;
import org.babyfish.jimmer.runtime.DraftSpi;
import org.babyfish.jimmer.runtime.ImmutableSpi;
import org.babyfish.jimmer.runtime.Internal;
import org.babyfish.jimmer.runtime.Visibility;
import org.babyfish.jimmer.sql.ManyToOne;
import org.jetbrains.annotations.Nullable;

@GeneratedBy(
        type = Message.class
)
public interface MessageDraft extends Message, BaseDraft {
    MessageDraft.Producer $ = Producer.INSTANCE;

    @OldChain
    MessageDraft setId(long id);

    @OldChain
    MessageDraft setCreateTime(Date createTime);

    @OldChain
    MessageDraft setModifyTime(Date modifyTime);

    UserDraft sender();

    UserDraft sender(boolean autoCreate);

    @OldChain
    MessageDraft setSender(User sender);

    @OldChain
    MessageDraft applySender(DraftConsumer<UserDraft> block);

    @OldChain
    MessageDraft applySender(User base, DraftConsumer<UserDraft> block);

    @OldChain
    MessageDraft setSenderId(long senderId);

    UserDraft recipient();

    UserDraft recipient(boolean autoCreate);

    @OldChain
    MessageDraft setRecipient(User recipient);

    @OldChain
    MessageDraft applyRecipient(DraftConsumer<UserDraft> block);

    @OldChain
    MessageDraft applyRecipient(User base, DraftConsumer<UserDraft> block);

    @OldChain
    MessageDraft setRecipientId(long recipientId);

    @OldChain
    MessageDraft setContent(String content);

    @OldChain
    MessageDraft setImages(String images);

    @GeneratedBy(
            type = Message.class
    )
    class Producer {
        static final Producer INSTANCE = new Producer();

        public static final int SLOT_ID = 3;

        public static final int SLOT_CREATE_TIME = 0;

        public static final int SLOT_MODIFY_TIME = 1;

        public static final int SLOT_BETWEEN_TIME = 2;

        public static final int SLOT_SENDER = 4;

        public static final int SLOT_SENDER_ID = 5;

        public static final int SLOT_RECIPIENT = 6;

        public static final int SLOT_RECIPIENT_ID = 7;

        public static final int SLOT_CONTENT = 8;

        public static final int SLOT_IMAGES = 9;

        public static final ImmutableType TYPE = ImmutableType
            .newBuilder(
                "0.8.150",
                Message.class,
                Collections.singleton(BaseDraft.Producer.TYPE),
                (ctx, base) -> new DraftImpl(ctx, (Message)base)
            )
            .redefine("createTime", SLOT_CREATE_TIME)
            .redefine("modifyTime", SLOT_MODIFY_TIME)
            .redefine("betweenTime", SLOT_BETWEEN_TIME)
            .id(SLOT_ID, "id", long.class)
            .add(SLOT_SENDER, "sender", ManyToOne.class, User.class, false)
            .add(SLOT_SENDER_ID, "senderId", ImmutablePropCategory.SCALAR, long.class, false)
            .add(SLOT_RECIPIENT, "recipient", ManyToOne.class, User.class, false)
            .add(SLOT_RECIPIENT_ID, "recipientId", ImmutablePropCategory.SCALAR, long.class, false)
            .add(SLOT_CONTENT, "content", ImmutablePropCategory.SCALAR, String.class, true)
            .add(SLOT_IMAGES, "images", ImmutablePropCategory.SCALAR, String.class, true)
            .build();

        private Producer() {
        }

        public Message produce(DraftConsumer<MessageDraft> block) {
            return produce(null, block);
        }

        public Message produce(Message base, DraftConsumer<MessageDraft> block) {
            return (Message)Internal.produce(TYPE, base, block);
        }

        @GeneratedBy(
                type = Message.class
        )
        @JsonPropertyOrder({"dummyPropForJacksonError__", "createTime", "modifyTime", "betweenTime", "id", "sender", "senderId", "recipient", "recipientId", "content", "images"})
        public abstract interface Implementor extends Message, ImmutableSpi {
            @Override
            default Object __get(PropId prop) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __get(prop.asName());
                    case SLOT_CREATE_TIME:
                    		return createTime();
                    case SLOT_MODIFY_TIME:
                    		return modifyTime();
                    case SLOT_BETWEEN_TIME:
                    		return betweenTime();
                    case SLOT_ID:
                    		return (Long)id();
                    case SLOT_SENDER:
                    		return sender();
                    case SLOT_SENDER_ID:
                    		return (Long)senderId();
                    case SLOT_RECIPIENT:
                    		return recipient();
                    case SLOT_RECIPIENT_ID:
                    		return (Long)recipientId();
                    case SLOT_CONTENT:
                    		return content();
                    case SLOT_IMAGES:
                    		return images();
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Message\": \"" + prop + "\"");
                }
            }

            @Override
            default Object __get(String prop) {
                switch (prop) {
                    case "createTime":
                    		return createTime();
                    case "modifyTime":
                    		return modifyTime();
                    case "betweenTime":
                    		return betweenTime();
                    case "id":
                    		return (Long)id();
                    case "sender":
                    		return sender();
                    case "senderId":
                    		return (Long)senderId();
                    case "recipient":
                    		return recipient();
                    case "recipientId":
                    		return (Long)recipientId();
                    case "content":
                    		return content();
                    case "images":
                    		return images();
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Message\": \"" + prop + "\"");
                }
            }

            default long getId() {
                return id();
            }

            default Date getCreateTime() {
                return createTime();
            }

            @Nullable
            default Date getModifyTime() {
                return modifyTime();
            }

            default String getBetweenTime() {
                return betweenTime();
            }

            default User getSender() {
                return sender();
            }

            default long getSenderId() {
                return senderId();
            }

            default User getRecipient() {
                return recipient();
            }

            default long getRecipientId() {
                return recipientId();
            }

            @Nullable
            default String getContent() {
                return content();
            }

            @Nullable
            default String getImages() {
                return images();
            }

            @Override
            default ImmutableType __type() {
                return TYPE;
            }

            default int getDummyPropForJacksonError__() {
                throw new ImmutableModuleRequiredException();
            }
        }

        @GeneratedBy(
                type = Message.class
        )
        private static class Impl implements Implementor, Cloneable, Serializable {
            private Visibility __visibility;

            long __idValue;

            boolean __idLoaded = false;

            Date __createTimeValue;

            Date __modifyTimeValue;

            boolean __modifyTimeLoaded = false;

            User __senderValue;

            User __recipientValue;

            String __contentValue;

            boolean __contentLoaded = false;

            String __imagesValue;

            boolean __imagesLoaded = false;

            Impl() {
                __visibility = Visibility.of(10);
                __visibility.show(SLOT_BETWEEN_TIME, false);
                __visibility.show(SLOT_SENDER_ID, false);
                __visibility.show(SLOT_RECIPIENT_ID, false);
            }

            @Override
            @JsonIgnore
            public long id() {
                if (!__idLoaded) {
                    throw new UnloadedException(Message.class, "id");
                }
                return __idValue;
            }

            @Override
            @JsonIgnore
            public Date createTime() {
                if (__createTimeValue == null) {
                    throw new UnloadedException(Message.class, "createTime");
                }
                return __createTimeValue;
            }

            @Override
            @JsonIgnore
            @Nullable
            public Date modifyTime() {
                if (!__modifyTimeLoaded) {
                    throw new UnloadedException(Message.class, "modifyTime");
                }
                return __modifyTimeValue;
            }

            @Override
            @JsonIgnore
            public User sender() {
                if (__senderValue == null) {
                    throw new UnloadedException(Message.class, "sender");
                }
                return __senderValue;
            }

            @Override
            @JsonIgnore
            public long senderId() {
                User __target = sender();
                return __target.id();
            }

            @Override
            @JsonIgnore
            public User recipient() {
                if (__recipientValue == null) {
                    throw new UnloadedException(Message.class, "recipient");
                }
                return __recipientValue;
            }

            @Override
            @JsonIgnore
            public long recipientId() {
                User __target = recipient();
                return __target.id();
            }

            @Override
            @JsonIgnore
            @Nullable
            public String content() {
                if (!__contentLoaded) {
                    throw new UnloadedException(Message.class, "content");
                }
                return __contentValue;
            }

            @Override
            @JsonIgnore
            @Nullable
            public String images() {
                if (!__imagesLoaded) {
                    throw new UnloadedException(Message.class, "images");
                }
                return __imagesValue;
            }

            @Override
            public Impl clone() {
                try {
                    return (Impl)super.clone();
                } catch(CloneNotSupportedException ex) {
                    throw new AssertionError(ex);
                }
            }

            @Override
            public boolean __isLoaded(PropId prop) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __isLoaded(prop.asName());
                    case SLOT_CREATE_TIME:
                    		return __createTimeValue != null;
                    case SLOT_MODIFY_TIME:
                    		return __modifyTimeLoaded;
                    case SLOT_BETWEEN_TIME:
                    		return __isLoaded(PropId.byIndex(SLOT_CREATE_TIME));
                    case SLOT_ID:
                    		return __idLoaded;
                    case SLOT_SENDER:
                    		return __senderValue != null;
                    case SLOT_SENDER_ID:
                    		return __isLoaded(PropId.byIndex(SLOT_SENDER)) && (sender() == null || 
                            	((ImmutableSpi)sender()).__isLoaded(PropId.byIndex(UserDraft.Producer.SLOT_ID)));
                    case SLOT_RECIPIENT:
                    		return __recipientValue != null;
                    case SLOT_RECIPIENT_ID:
                    		return __isLoaded(PropId.byIndex(SLOT_RECIPIENT)) && (recipient() == null || 
                            	((ImmutableSpi)recipient()).__isLoaded(PropId.byIndex(UserDraft.Producer.SLOT_ID)));
                    case SLOT_CONTENT:
                    		return __contentLoaded;
                    case SLOT_IMAGES:
                    		return __imagesLoaded;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Message\": \"" + prop + "\"");
                }
            }

            @Override
            public boolean __isLoaded(String prop) {
                switch (prop) {
                    case "createTime":
                    		return __createTimeValue != null;
                    case "modifyTime":
                    		return __modifyTimeLoaded;
                    case "betweenTime":
                    		return __isLoaded(PropId.byIndex(SLOT_CREATE_TIME));
                    case "id":
                    		return __idLoaded;
                    case "sender":
                    		return __senderValue != null;
                    case "senderId":
                    		return __isLoaded(PropId.byIndex(SLOT_SENDER)) && (sender() == null || 
                            	((ImmutableSpi)sender()).__isLoaded(PropId.byIndex(UserDraft.Producer.SLOT_ID)));
                    case "recipient":
                    		return __recipientValue != null;
                    case "recipientId":
                    		return __isLoaded(PropId.byIndex(SLOT_RECIPIENT)) && (recipient() == null || 
                            	((ImmutableSpi)recipient()).__isLoaded(PropId.byIndex(UserDraft.Producer.SLOT_ID)));
                    case "content":
                    		return __contentLoaded;
                    case "images":
                    		return __imagesLoaded;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Message\": \"" + prop + "\"");
                }
            }

            @Override
            public boolean __isVisible(PropId prop) {
                if (__visibility == null) {
                    return true;
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __isVisible(prop.asName());
                    case SLOT_CREATE_TIME:
                    		return __visibility.visible(SLOT_CREATE_TIME);
                    case SLOT_MODIFY_TIME:
                    		return __visibility.visible(SLOT_MODIFY_TIME);
                    case SLOT_BETWEEN_TIME:
                    		return __visibility.visible(SLOT_BETWEEN_TIME);
                    case SLOT_ID:
                    		return __visibility.visible(SLOT_ID);
                    case SLOT_SENDER:
                    		return __visibility.visible(SLOT_SENDER);
                    case SLOT_SENDER_ID:
                    		return __visibility.visible(SLOT_SENDER_ID);
                    case SLOT_RECIPIENT:
                    		return __visibility.visible(SLOT_RECIPIENT);
                    case SLOT_RECIPIENT_ID:
                    		return __visibility.visible(SLOT_RECIPIENT_ID);
                    case SLOT_CONTENT:
                    		return __visibility.visible(SLOT_CONTENT);
                    case SLOT_IMAGES:
                    		return __visibility.visible(SLOT_IMAGES);
                    default: return true;
                }
            }

            @Override
            public boolean __isVisible(String prop) {
                if (__visibility == null) {
                    return true;
                }
                switch (prop) {
                    case "createTime":
                    		return __visibility.visible(SLOT_CREATE_TIME);
                    case "modifyTime":
                    		return __visibility.visible(SLOT_MODIFY_TIME);
                    case "betweenTime":
                    		return __visibility.visible(SLOT_BETWEEN_TIME);
                    case "id":
                    		return __visibility.visible(SLOT_ID);
                    case "sender":
                    		return __visibility.visible(SLOT_SENDER);
                    case "senderId":
                    		return __visibility.visible(SLOT_SENDER_ID);
                    case "recipient":
                    		return __visibility.visible(SLOT_RECIPIENT);
                    case "recipientId":
                    		return __visibility.visible(SLOT_RECIPIENT_ID);
                    case "content":
                    		return __visibility.visible(SLOT_CONTENT);
                    case "images":
                    		return __visibility.visible(SLOT_IMAGES);
                    default: return true;
                }
            }

            @Override
            public int hashCode() {
                int hash = __visibility != null ? __visibility.hashCode() : 0;
                if (__idLoaded) {
                    hash = 31 * hash + Long.hashCode(__idValue);
                    // If entity-id is loaded, return directly
                    return hash;
                }
                if (__createTimeValue != null) {
                    hash = 31 * hash + __createTimeValue.hashCode();
                }
                if (__modifyTimeLoaded && __modifyTimeValue != null) {
                    hash = 31 * hash + __modifyTimeValue.hashCode();
                }
                if (__senderValue != null) {
                    hash = 31 * hash + __senderValue.hashCode();
                }
                if (__recipientValue != null) {
                    hash = 31 * hash + __recipientValue.hashCode();
                }
                if (__contentLoaded && __contentValue != null) {
                    hash = 31 * hash + __contentValue.hashCode();
                }
                if (__imagesLoaded && __imagesValue != null) {
                    hash = 31 * hash + __imagesValue.hashCode();
                }
                return hash;
            }

            private int __shallowHashCode() {
                int hash = __visibility != null ? __visibility.hashCode() : 0;
                if (__idLoaded) {
                    hash = 31 * hash + Long.hashCode(__idValue);
                }
                if (__createTimeValue != null) {
                    hash = 31 * hash + System.identityHashCode(__createTimeValue);
                }
                if (__modifyTimeLoaded) {
                    hash = 31 * hash + System.identityHashCode(__modifyTimeValue);
                }
                if (__senderValue != null) {
                    hash = 31 * hash + System.identityHashCode(__senderValue);
                }
                if (__recipientValue != null) {
                    hash = 31 * hash + System.identityHashCode(__recipientValue);
                }
                if (__contentLoaded) {
                    hash = 31 * hash + System.identityHashCode(__contentValue);
                }
                if (__imagesLoaded) {
                    hash = 31 * hash + System.identityHashCode(__imagesValue);
                }
                return hash;
            }

            @Override
            public int __hashCode(boolean shallow) {
                return shallow ? __shallowHashCode() : hashCode();
            }

            @Override
            public boolean equals(Object obj) {
                if (obj == null || !(obj instanceof Implementor)) {
                    return false;
                }
                Implementor __other = (Implementor)obj;
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                boolean __idLoaded = this.__idLoaded;
                if (__idLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                if (__idLoaded) {
                    // If entity-id is loaded, return directly
                    return __idValue == __other.id();
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATE_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                boolean __createTimeLoaded = __createTimeValue != null;
                if (__createTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                if (__createTimeLoaded && !Objects.equals(__createTimeValue, __other.createTime())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_MODIFY_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                boolean __modifyTimeLoaded = this.__modifyTimeLoaded;
                if (__modifyTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                if (__modifyTimeLoaded && !Objects.equals(__modifyTimeValue, __other.modifyTime())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_SENDER)) != __other.__isVisible(PropId.byIndex(SLOT_SENDER))) {
                    return false;
                }
                boolean __senderLoaded = __senderValue != null;
                if (__senderLoaded != __other.__isLoaded(PropId.byIndex(SLOT_SENDER))) {
                    return false;
                }
                if (__senderLoaded && !Objects.equals(__senderValue, __other.sender())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_SENDER_ID)) != __other.__isVisible(PropId.byIndex(SLOT_SENDER_ID))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_RECIPIENT)) != __other.__isVisible(PropId.byIndex(SLOT_RECIPIENT))) {
                    return false;
                }
                boolean __recipientLoaded = __recipientValue != null;
                if (__recipientLoaded != __other.__isLoaded(PropId.byIndex(SLOT_RECIPIENT))) {
                    return false;
                }
                if (__recipientLoaded && !Objects.equals(__recipientValue, __other.recipient())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_RECIPIENT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_RECIPIENT_ID))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_CONTENT)) != __other.__isVisible(PropId.byIndex(SLOT_CONTENT))) {
                    return false;
                }
                boolean __contentLoaded = this.__contentLoaded;
                if (__contentLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CONTENT))) {
                    return false;
                }
                if (__contentLoaded && !Objects.equals(__contentValue, __other.content())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_IMAGES)) != __other.__isVisible(PropId.byIndex(SLOT_IMAGES))) {
                    return false;
                }
                boolean __imagesLoaded = this.__imagesLoaded;
                if (__imagesLoaded != __other.__isLoaded(PropId.byIndex(SLOT_IMAGES))) {
                    return false;
                }
                if (__imagesLoaded && !Objects.equals(__imagesValue, __other.images())) {
                    return false;
                }
                return true;
            }

            private boolean __shallowEquals(Object obj) {
                if (obj == null || !(obj instanceof Implementor)) {
                    return false;
                }
                Implementor __other = (Implementor)obj;
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                boolean __idLoaded = this.__idLoaded;
                if (__idLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                if (__idLoaded && __idValue != __other.id()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATE_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                boolean __createTimeLoaded = __createTimeValue != null;
                if (__createTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                if (__createTimeLoaded && __createTimeValue != __other.createTime()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_MODIFY_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                boolean __modifyTimeLoaded = this.__modifyTimeLoaded;
                if (__modifyTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                if (__modifyTimeLoaded && __modifyTimeValue != __other.modifyTime()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_SENDER)) != __other.__isVisible(PropId.byIndex(SLOT_SENDER))) {
                    return false;
                }
                boolean __senderLoaded = __senderValue != null;
                if (__senderLoaded != __other.__isLoaded(PropId.byIndex(SLOT_SENDER))) {
                    return false;
                }
                if (__senderLoaded && __senderValue != __other.sender()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_SENDER_ID)) != __other.__isVisible(PropId.byIndex(SLOT_SENDER_ID))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_RECIPIENT)) != __other.__isVisible(PropId.byIndex(SLOT_RECIPIENT))) {
                    return false;
                }
                boolean __recipientLoaded = __recipientValue != null;
                if (__recipientLoaded != __other.__isLoaded(PropId.byIndex(SLOT_RECIPIENT))) {
                    return false;
                }
                if (__recipientLoaded && __recipientValue != __other.recipient()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_RECIPIENT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_RECIPIENT_ID))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_CONTENT)) != __other.__isVisible(PropId.byIndex(SLOT_CONTENT))) {
                    return false;
                }
                boolean __contentLoaded = this.__contentLoaded;
                if (__contentLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CONTENT))) {
                    return false;
                }
                if (__contentLoaded && __contentValue != __other.content()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_IMAGES)) != __other.__isVisible(PropId.byIndex(SLOT_IMAGES))) {
                    return false;
                }
                boolean __imagesLoaded = this.__imagesLoaded;
                if (__imagesLoaded != __other.__isLoaded(PropId.byIndex(SLOT_IMAGES))) {
                    return false;
                }
                if (__imagesLoaded && __imagesValue != __other.images()) {
                    return false;
                }
                return true;
            }

            @Override
            public boolean __equals(Object obj, boolean shallow) {
                return shallow ? __shallowEquals(obj) : equals(obj);
            }

            @Override
            public String toString() {
                return ImmutableObjects.toString(this);
            }
        }

        @GeneratedBy(
                type = Message.class
        )
        private static class DraftImpl implements Implementor, DraftSpi, MessageDraft {
            private DraftContext __ctx;

            private Impl __base;

            private Impl __modified;

            private boolean __resolving;

            private Message __resolved;

            DraftImpl(DraftContext ctx, Message base) {
                __ctx = ctx;
                if (base != null) {
                    __base = (Impl)base;
                }
                else {
                    __modified = new Impl();
                }
            }

            @Override
            public boolean __isLoaded(PropId prop) {
                return (__modified!= null ? __modified : __base).__isLoaded(prop);
            }

            @Override
            public boolean __isLoaded(String prop) {
                return (__modified!= null ? __modified : __base).__isLoaded(prop);
            }

            @Override
            public boolean __isVisible(PropId prop) {
                return (__modified!= null ? __modified : __base).__isVisible(prop);
            }

            @Override
            public boolean __isVisible(String prop) {
                return (__modified!= null ? __modified : __base).__isVisible(prop);
            }

            @Override
            public int hashCode() {
                return (__modified!= null ? __modified : __base).hashCode();
            }

            @Override
            public int __hashCode(boolean shallow) {
                return (__modified!= null ? __modified : __base).__hashCode(shallow);
            }

            @Override
            public boolean equals(Object obj) {
                return (__modified!= null ? __modified : __base).equals(obj);
            }

            @Override
            public boolean __equals(Object obj, boolean shallow) {
                return (__modified!= null ? __modified : __base).__equals(obj, shallow);
            }

            @Override
            public String toString() {
                return ImmutableObjects.toString(this);
            }

            @Override
            @JsonIgnore
            public long id() {
                return (__modified!= null ? __modified : __base).id();
            }

            @Override
            public MessageDraft setId(long id) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__idValue = id;
                __tmpModified.__idLoaded = true;
                return this;
            }

            @Override
            @JsonIgnore
            public Date createTime() {
                return (__modified!= null ? __modified : __base).createTime();
            }

            @Override
            public MessageDraft setCreateTime(Date createTime) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (createTime == null) {
                    throw new IllegalArgumentException(
                        "'createTime' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__createTimeValue = createTime;
                return this;
            }

            @Override
            @JsonIgnore
            @Nullable
            public Date modifyTime() {
                return (__modified!= null ? __modified : __base).modifyTime();
            }

            @Override
            public MessageDraft setModifyTime(Date modifyTime) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__modifyTimeValue = modifyTime;
                __tmpModified.__modifyTimeLoaded = true;
                return this;
            }

            @Override
            @JsonIgnore
            public String betweenTime() {
                return (__modified!= null ? __modified : __base).betweenTime();
            }

            @Override
            @JsonIgnore
            public UserDraft sender() {
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).sender());
            }

            @Override
            public UserDraft sender(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_SENDER)))) {
                    setSender(UserDraft.$.produce(null, null));
                }
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).sender());
            }

            @Override
            public MessageDraft setSender(User sender) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (sender == null) {
                    throw new IllegalArgumentException(
                        "'sender' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__senderValue = sender;
                return this;
            }

            @Override
            public MessageDraft applySender(DraftConsumer<UserDraft> block) {
                applySender(null, block);
                return this;
            }

            @Override
            public MessageDraft applySender(User base, DraftConsumer<UserDraft> block) {
                setSender(UserDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public long senderId() {
                User __target = sender();
                return __target.id();
            }

            @Override
            public MessageDraft setSenderId(long senderId) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                setSender(ImmutableObjects.makeIdOnly(User.class, senderId));
                return this;
            }

            @Override
            @JsonIgnore
            public UserDraft recipient() {
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).recipient());
            }

            @Override
            public UserDraft recipient(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_RECIPIENT)))) {
                    setRecipient(UserDraft.$.produce(null, null));
                }
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).recipient());
            }

            @Override
            public MessageDraft setRecipient(User recipient) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (recipient == null) {
                    throw new IllegalArgumentException(
                        "'recipient' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__recipientValue = recipient;
                return this;
            }

            @Override
            public MessageDraft applyRecipient(DraftConsumer<UserDraft> block) {
                applyRecipient(null, block);
                return this;
            }

            @Override
            public MessageDraft applyRecipient(User base, DraftConsumer<UserDraft> block) {
                setRecipient(UserDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public long recipientId() {
                User __target = recipient();
                return __target.id();
            }

            @Override
            public MessageDraft setRecipientId(long recipientId) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                setRecipient(ImmutableObjects.makeIdOnly(User.class, recipientId));
                return this;
            }

            @Override
            @JsonIgnore
            @Nullable
            public String content() {
                return (__modified!= null ? __modified : __base).content();
            }

            @Override
            public MessageDraft setContent(String content) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__contentValue = content;
                __tmpModified.__contentLoaded = true;
                return this;
            }

            @Override
            @JsonIgnore
            @Nullable
            public String images() {
                return (__modified!= null ? __modified : __base).images();
            }

            @Override
            public MessageDraft setImages(String images) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__imagesValue = images;
                __tmpModified.__imagesLoaded = true;
                return this;
            }

            @SuppressWarnings("unchecked")
            @Override
            public void __set(PropId prop, Object value) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__set(prop.asName(), value);
                    return;
                    case SLOT_CREATE_TIME:
                    		setCreateTime((Date)value);break;
                    case SLOT_MODIFY_TIME:
                    		setModifyTime((Date)value);break;
                    case SLOT_BETWEEN_TIME:
                    		break;
                    case SLOT_ID:
                    		if (value == null) throw new IllegalArgumentException("'id' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setId((Long)value);
                            break;
                    case SLOT_SENDER:
                    		setSender((User)value);break;
                    case SLOT_SENDER_ID:
                    		if (value == null) throw new IllegalArgumentException("'senderId' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setSenderId((Long)value);
                            break;
                    case SLOT_RECIPIENT:
                    		setRecipient((User)value);break;
                    case SLOT_RECIPIENT_ID:
                    		if (value == null) throw new IllegalArgumentException("'recipientId' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setRecipientId((Long)value);
                            break;
                    case SLOT_CONTENT:
                    		setContent((String)value);break;
                    case SLOT_IMAGES:
                    		setImages((String)value);break;
                    default: throw new IllegalArgumentException("Illegal property id for \"com.celeste.entity.Message\": \"" + prop + "\"");
                }
            }

            @SuppressWarnings("unchecked")
            @Override
            public void __set(String prop, Object value) {
                switch (prop) {
                    case "createTime":
                    		setCreateTime((Date)value);break;
                    case "modifyTime":
                    		setModifyTime((Date)value);break;
                    case "betweenTime":
                    		break;
                    case "id":
                    		if (value == null) throw new IllegalArgumentException("'id' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setId((Long)value);
                            break;
                    case "sender":
                    		setSender((User)value);break;
                    case "senderId":
                    		if (value == null) throw new IllegalArgumentException("'senderId' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setSenderId((Long)value);
                            break;
                    case "recipient":
                    		setRecipient((User)value);break;
                    case "recipientId":
                    		if (value == null) throw new IllegalArgumentException("'recipientId' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setRecipientId((Long)value);
                            break;
                    case "content":
                    		setContent((String)value);break;
                    case "images":
                    		setImages((String)value);break;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Message\": \"" + prop + "\"");
                }
            }

            @Override
            public void __show(PropId prop, boolean visible) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Visibility __visibility = (__modified!= null ? __modified : __base).__visibility;
                if (__visibility == null) {
                    if (visible) {
                        return;
                    }
                    __modified().__visibility = __visibility = Visibility.of(10);
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__show(prop.asName(), visible);
                    return;
                    case SLOT_CREATE_TIME:
                    		__visibility.show(SLOT_CREATE_TIME, visible);break;
                    case SLOT_MODIFY_TIME:
                    		__visibility.show(SLOT_MODIFY_TIME, visible);break;
                    case SLOT_BETWEEN_TIME:
                    		__visibility.show(SLOT_BETWEEN_TIME, visible);break;
                    case SLOT_ID:
                    		__visibility.show(SLOT_ID, visible);break;
                    case SLOT_SENDER:
                    		__visibility.show(SLOT_SENDER, visible);break;
                    case SLOT_SENDER_ID:
                    		__visibility.show(SLOT_SENDER_ID, visible);break;
                    case SLOT_RECIPIENT:
                    		__visibility.show(SLOT_RECIPIENT, visible);break;
                    case SLOT_RECIPIENT_ID:
                    		__visibility.show(SLOT_RECIPIENT_ID, visible);break;
                    case SLOT_CONTENT:
                    		__visibility.show(SLOT_CONTENT, visible);break;
                    case SLOT_IMAGES:
                    		__visibility.show(SLOT_IMAGES, visible);break;
                    default: throw new IllegalArgumentException(
                                "Illegal property id for \"com.celeste.entity.Message\": \"" + 
                                prop + 
                                "\",it does not exists"
                            );
                }
            }

            @Override
            public void __show(String prop, boolean visible) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Visibility __visibility = (__modified!= null ? __modified : __base).__visibility;
                if (__visibility == null) {
                    if (visible) {
                        return;
                    }
                    __modified().__visibility = __visibility = Visibility.of(10);
                }
                switch (prop) {
                    case "createTime":
                    		__visibility.show(SLOT_CREATE_TIME, visible);break;
                    case "modifyTime":
                    		__visibility.show(SLOT_MODIFY_TIME, visible);break;
                    case "betweenTime":
                    		__visibility.show(SLOT_BETWEEN_TIME, visible);break;
                    case "id":
                    		__visibility.show(SLOT_ID, visible);break;
                    case "sender":
                    		__visibility.show(SLOT_SENDER, visible);break;
                    case "senderId":
                    		__visibility.show(SLOT_SENDER_ID, visible);break;
                    case "recipient":
                    		__visibility.show(SLOT_RECIPIENT, visible);break;
                    case "recipientId":
                    		__visibility.show(SLOT_RECIPIENT_ID, visible);break;
                    case "content":
                    		__visibility.show(SLOT_CONTENT, visible);break;
                    case "images":
                    		__visibility.show(SLOT_IMAGES, visible);break;
                    default: throw new IllegalArgumentException(
                                "Illegal property name for \"com.celeste.entity.Message\": \"" + 
                                prop + 
                                "\",it does not exists"
                            );
                }
            }

            @Override
            public void __unload(PropId prop) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__unload(prop.asName());
                    return;
                    case SLOT_CREATE_TIME:
                    		__modified().__createTimeValue = null;break;
                    case SLOT_MODIFY_TIME:
                    		__modified().__modifyTimeLoaded = false;break;
                    case SLOT_BETWEEN_TIME:
                    		break;
                    case SLOT_ID:
                    		__modified().__idLoaded = false;break;
                    case SLOT_SENDER:
                    		__modified().__senderValue = null;break;
                    case SLOT_SENDER_ID:
                    		__unload(PropId.byIndex(SLOT_SENDER));break;
                    case SLOT_RECIPIENT:
                    		__modified().__recipientValue = null;break;
                    case SLOT_RECIPIENT_ID:
                    		__unload(PropId.byIndex(SLOT_RECIPIENT));break;
                    case SLOT_CONTENT:
                    		__modified().__contentLoaded = false;break;
                    case SLOT_IMAGES:
                    		__modified().__imagesLoaded = false;break;
                    default: throw new IllegalArgumentException("Illegal property id for \"com.celeste.entity.Message\": \"" + prop + "\", it does not exist or its loaded state is not controllable");
                }
            }

            @Override
            public void __unload(String prop) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                switch (prop) {
                    case "createTime":
                    		__modified().__createTimeValue = null;break;
                    case "modifyTime":
                    		__modified().__modifyTimeLoaded = false;break;
                    case "betweenTime":
                    		break;
                    case "id":
                    		__modified().__idLoaded = false;break;
                    case "sender":
                    		__modified().__senderValue = null;break;
                    case "senderId":
                    		__unload(PropId.byIndex(SLOT_SENDER));break;
                    case "recipient":
                    		__modified().__recipientValue = null;break;
                    case "recipientId":
                    		__unload(PropId.byIndex(SLOT_RECIPIENT));break;
                    case "content":
                    		__modified().__contentLoaded = false;break;
                    case "images":
                    		__modified().__imagesLoaded = false;break;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.Message\": \"" + prop + "\", it does not exist or its loaded state is not controllable");
                }
            }

            @Override
            public DraftContext __draftContext() {
                return __ctx;
            }

            @Override
            public Object __resolve() {
                if (__resolved != null) {
                    return __resolved;
                }
                if (__resolving) {
                    throw new CircularReferenceException();
                }
                __resolving = true;
                try {
                    Implementor base = __base;
                    Impl __tmpModified = __modified;
                    if (__tmpModified == null) {
                        if (base.__isLoaded(PropId.byIndex(SLOT_SENDER))) {
                            User oldValue = base.sender();
                            User newValue = __ctx.resolveObject(oldValue);
                            if (oldValue != newValue) {
                                setSender(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_RECIPIENT))) {
                            User oldValue = base.recipient();
                            User newValue = __ctx.resolveObject(oldValue);
                            if (oldValue != newValue) {
                                setRecipient(newValue);
                            }
                        }
                        __tmpModified = __modified;
                    }
                    else {
                        __tmpModified.__senderValue = __ctx.resolveObject(__tmpModified.__senderValue);
                        __tmpModified.__recipientValue = __ctx.resolveObject(__tmpModified.__recipientValue);
                    }
                    if (__base != null && __tmpModified == null) {
                        this.__resolved = base;
                        return base;
                    }
                    this.__resolved = __tmpModified;
                    return __tmpModified;
                }
                finally {
                    __resolving = false;
                }
            }

            @Override
            public boolean __isResolved() {
                return __resolved != null;
            }

            Impl __modified() {
                Impl __tmpModified = __modified;
                if (__tmpModified == null) {
                    __tmpModified = __base.clone();
                    __modified = __tmpModified;
                }
                return __tmpModified;
            }
        }
    }

    @GeneratedBy(
            type = Message.class
    )
    class Builder {
        private final Producer.DraftImpl __draft;

        public Builder() {
            __draft = new Producer.DraftImpl(null, null);
            __draft.__show(PropId.byIndex(Producer.SLOT_CREATE_TIME), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_BETWEEN_TIME), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_SENDER), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_SENDER_ID), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_RECIPIENT), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_RECIPIENT_ID), false);
        }

        public Builder id(Long id) {
            if (id != null) {
                __draft.setId(id);
            }
            return this;
        }

        public Builder createTime(Date createTime) {
            if (createTime != null) {
                __draft.setCreateTime(createTime);
                __draft.__show(PropId.byIndex(Producer.SLOT_CREATE_TIME), true);
            }
            return this;
        }

        @Nullable
        public Builder modifyTime(Date modifyTime) {
            __draft.setModifyTime(modifyTime);
            return this;
        }

        public Builder sender(User sender) {
            if (sender != null) {
                __draft.setSender(sender);
                __draft.__show(PropId.byIndex(Producer.SLOT_SENDER), true);
            }
            return this;
        }

        public Builder senderId(Long senderId) {
            if (senderId != null) {
                __draft.setSenderId(senderId);
                __draft.__show(PropId.byIndex(Producer.SLOT_SENDER_ID), true);
            }
            return this;
        }

        public Builder recipient(User recipient) {
            if (recipient != null) {
                __draft.setRecipient(recipient);
                __draft.__show(PropId.byIndex(Producer.SLOT_RECIPIENT), true);
            }
            return this;
        }

        public Builder recipientId(Long recipientId) {
            if (recipientId != null) {
                __draft.setRecipientId(recipientId);
                __draft.__show(PropId.byIndex(Producer.SLOT_RECIPIENT_ID), true);
            }
            return this;
        }

        @Nullable
        public Builder content(String content) {
            __draft.setContent(content);
            return this;
        }

        @Nullable
        public Builder images(String images) {
            __draft.setImages(images);
            return this;
        }

        public Message build() {
            return (Message)__draft.__modified();
        }
    }
}
