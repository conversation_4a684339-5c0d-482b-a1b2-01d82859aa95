package com.celeste.entity;

import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.sql.JoinType;
import org.babyfish.jimmer.sql.ast.impl.table.TableImplementor;
import org.babyfish.jimmer.sql.ast.impl.table.TableProxies;
import org.babyfish.jimmer.sql.ast.table.Table;
import org.babyfish.jimmer.sql.ast.table.TableEx;
import org.babyfish.jimmer.sql.ast.table.WeakJoin;
import org.babyfish.jimmer.sql.ast.table.spi.AbstractTypedTable;

@GeneratedBy(
        type = CommentLike.class
)
public class CommentLikeTableEx extends CommentLikeTable implements TableEx<CommentLike> {
    public static final CommentLikeTableEx $ = new CommentLikeTableEx(CommentLikeTable.$, null);

    public CommentLikeTableEx() {
        super();
    }

    public CommentLikeTableEx(AbstractTypedTable.DelayedOperation<CommentLike> delayedOperation) {
        super(delayedOperation);
    }

    public CommentLikeTableEx(TableImplementor<CommentLike> table) {
        super(table);
    }

    protected CommentLikeTableEx(CommentLikeTable base, String joinDisabledReason) {
        super(base, joinDisabledReason);
    }

    public UserTableEx user() {
        __beforeJoin();
        if (raw != null) {
            return new UserTableEx(raw.joinImplementor(CommentLikeProps.USER.unwrap()));
        }
        return new UserTableEx(joinOperation(CommentLikeProps.USER.unwrap()));
    }

    public UserTableEx user(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new UserTableEx(raw.joinImplementor(CommentLikeProps.USER.unwrap(), joinType));
        }
        return new UserTableEx(joinOperation(CommentLikeProps.USER.unwrap(), joinType));
    }

    public CommentTableEx comment() {
        __beforeJoin();
        if (raw != null) {
            return new CommentTableEx(raw.joinImplementor(CommentLikeProps.COMMENT.unwrap()));
        }
        return new CommentTableEx(joinOperation(CommentLikeProps.COMMENT.unwrap()));
    }

    public CommentTableEx comment(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new CommentTableEx(raw.joinImplementor(CommentLikeProps.COMMENT.unwrap(), joinType));
        }
        return new CommentTableEx(joinOperation(CommentLikeProps.COMMENT.unwrap(), joinType));
    }

    @Override
    public CommentLikeTableEx asTableEx() {
        return this;
    }

    @Override
    public CommentLikeTableEx __disableJoin(String reason) {
        return new CommentLikeTableEx(this, reason);
    }

    public <TT extends Table<?>, WJ extends WeakJoin<CommentLikeTable, TT>> TT weakJoin(
            Class<WJ> weakJoinType) {
        return weakJoin(weakJoinType, JoinType.INNER);
    }

    @SuppressWarnings("unchecked")
    public <TT extends Table<?>, WJ extends WeakJoin<CommentLikeTable, TT>> TT weakJoin(
            Class<WJ> weakJoinType, JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return (TT)TableProxies.wrap(raw.weakJoinImplementor(weakJoinType, joinType));
        }
        return (TT)TableProxies.fluent(joinOperation(weakJoinType, joinType));
    }
}
