package com.celeste.entity;

import java.lang.Class;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.function.Function;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.sql.JoinType;
import org.babyfish.jimmer.sql.ast.Predicate;
import org.babyfish.jimmer.sql.ast.PropExpression;
import org.babyfish.jimmer.sql.ast.impl.table.TableImplementor;
import org.babyfish.jimmer.sql.ast.impl.table.TableProxies;
import org.babyfish.jimmer.sql.ast.table.Table;
import org.babyfish.jimmer.sql.ast.table.TableEx;
import org.babyfish.jimmer.sql.ast.table.WeakJoin;
import org.babyfish.jimmer.sql.ast.table.spi.AbstractTypedTable;

@GeneratedBy(
        type = Collection.class
)
public class CollectionTableEx extends CollectionTable implements TableEx<Collection> {
    public static final CollectionTableEx $ = new CollectionTableEx(CollectionTable.$, null);

    public CollectionTableEx() {
        super();
    }

    public CollectionTableEx(AbstractTypedTable.DelayedOperation<Collection> delayedOperation) {
        super(delayedOperation);
    }

    public CollectionTableEx(TableImplementor<Collection> table) {
        super(table);
    }

    protected CollectionTableEx(CollectionTable base, String joinDisabledReason) {
        super(base, joinDisabledReason);
    }

    public UserTableEx user() {
        __beforeJoin();
        if (raw != null) {
            return new UserTableEx(raw.joinImplementor(CollectionProps.USER.unwrap()));
        }
        return new UserTableEx(joinOperation(CollectionProps.USER.unwrap()));
    }

    public UserTableEx user(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new UserTableEx(raw.joinImplementor(CollectionProps.USER.unwrap(), joinType));
        }
        return new UserTableEx(joinOperation(CollectionProps.USER.unwrap(), joinType));
    }

    public VideoTableEx videos() {
        __beforeJoin();
        if (raw != null) {
            return new VideoTableEx(raw.joinImplementor(CollectionProps.VIDEOS.unwrap()));
        }
        return new VideoTableEx(joinOperation(CollectionProps.VIDEOS.unwrap()));
    }

    public VideoTableEx videos(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new VideoTableEx(raw.joinImplementor(CollectionProps.VIDEOS.unwrap(), joinType));
        }
        return new VideoTableEx(joinOperation(CollectionProps.VIDEOS.unwrap(), joinType));
    }

    @Override
    public Predicate videos(Function<VideoTableEx, Predicate> block) {
        return exists(CollectionProps.VIDEOS.unwrap(), block);
    }

    public PropExpression.Num<Long> videoIds() {
        return __getAssociatedId(CollectionProps.VIDEOS.unwrap());
    }

    @Override
    public CollectionTableEx asTableEx() {
        return this;
    }

    @Override
    public CollectionTableEx __disableJoin(String reason) {
        return new CollectionTableEx(this, reason);
    }

    public <TT extends Table<?>, WJ extends WeakJoin<CollectionTable, TT>> TT weakJoin(
            Class<WJ> weakJoinType) {
        return weakJoin(weakJoinType, JoinType.INNER);
    }

    @SuppressWarnings("unchecked")
    public <TT extends Table<?>, WJ extends WeakJoin<CollectionTable, TT>> TT weakJoin(
            Class<WJ> weakJoinType, JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return (TT)TableProxies.wrap(raw.weakJoinImplementor(weakJoinType, joinType));
        }
        return (TT)TableProxies.fluent(joinOperation(weakJoinType, joinType));
    }
}
