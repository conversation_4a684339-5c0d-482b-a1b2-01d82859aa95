package com.celeste.entity;

import java.lang.Long;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.meta.ImmutableType;
import org.babyfish.jimmer.meta.TypedProp;
import org.babyfish.jimmer.sql.JoinType;
import org.babyfish.jimmer.sql.ast.PropExpression;
import org.babyfish.jimmer.sql.ast.table.Props;
import org.babyfish.jimmer.sql.ast.table.PropsFor;

@GeneratedBy(
        type = CommentLike.class
)
@PropsFor(CommentLike.class)
public interface CommentLikeProps extends Props {
    TypedProp.Scalar<CommentLike, Long> ID = 
        TypedProp.scalar(ImmutableType.get(CommentLike.class).getProp("id"));

    TypedProp.Reference<CommentLike, User> USER = 
        TypedProp.reference(ImmutableType.get(CommentLike.class).getProp("user"));

    TypedProp.Reference<CommentLike, Comment> COMMENT = 
        TypedProp.reference(ImmutableType.get(CommentLike.class).getProp("comment"));

    PropExpression.Num<Long> id();

    UserTable user();

    UserTable user(JoinType joinType);

    PropExpression.Num<Long> userId();

    CommentTable comment();

    CommentTable comment(JoinType joinType);

    PropExpression.Num<Long> commentId();
}
