package com.celeste.entity;

import java.lang.Override;
import java.util.function.Consumer;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.lang.NewChain;
import org.babyfish.jimmer.meta.ImmutableProp;
import org.babyfish.jimmer.sql.ast.table.Table;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.babyfish.jimmer.sql.fetcher.FieldConfig;
import org.babyfish.jimmer.sql.fetcher.IdOnlyFetchType;
import org.babyfish.jimmer.sql.fetcher.impl.FetcherImpl;
import org.babyfish.jimmer.sql.fetcher.spi.AbstractTypedFetcher;

@GeneratedBy(
        type = Message.class
)
public class MessageFetcher extends AbstractTypedFetcher<Message, MessageFetcher> {
    public static final MessageFetcher $ = new MessageFetcher(null);

    private MessageFetcher(FetcherImpl<Message> base) {
        super(Message.class, base);
    }

    private MessageFetcher(MessageFetcher prev, ImmutableProp prop, boolean negative,
            IdOnlyFetchType idOnlyFetchType) {
        super(prev, prop, negative, idOnlyFetchType);
    }

    private MessageFetcher(MessageFetcher prev, ImmutableProp prop,
            FieldConfig<?, ? extends Table<?>> fieldConfig) {
        super(prev, prop, fieldConfig);
    }

    public static MessageFetcher $from(Fetcher<Message> base) {
        return base instanceof MessageFetcher ? 
        	(MessageFetcher)base : 
        	new MessageFetcher((FetcherImpl<Message>)base);
    }

    @NewChain
    public MessageFetcher createTime() {
        return add("createTime");
    }

    @NewChain
    public MessageFetcher createTime(boolean enabled) {
        return enabled ? add("createTime") : remove("createTime");
    }

    @NewChain
    public MessageFetcher modifyTime() {
        return add("modifyTime");
    }

    @NewChain
    public MessageFetcher modifyTime(boolean enabled) {
        return enabled ? add("modifyTime") : remove("modifyTime");
    }

    @NewChain
    public MessageFetcher betweenTime() {
        return add("betweenTime");
    }

    @NewChain
    public MessageFetcher betweenTime(boolean enabled) {
        return enabled ? add("betweenTime") : remove("betweenTime");
    }

    @NewChain
    public MessageFetcher sender() {
        return add("sender");
    }

    @NewChain
    public MessageFetcher sender(boolean enabled) {
        return enabled ? add("sender") : remove("sender");
    }

    @NewChain
    public MessageFetcher sender(Fetcher<User> childFetcher) {
        return add("sender", childFetcher);
    }

    @NewChain
    public MessageFetcher sender(Fetcher<User> childFetcher,
            Consumer<FieldConfig<User, UserTable>> fieldConfig) {
        return add("sender", childFetcher, fieldConfig);
    }

    @NewChain
    public MessageFetcher sender(IdOnlyFetchType idOnlyFetchType) {
        return add("sender", idOnlyFetchType);
    }

    @NewChain
    public MessageFetcher senderId() {
        return add("senderId");
    }

    @NewChain
    public MessageFetcher senderId(boolean enabled) {
        return enabled ? add("senderId") : remove("senderId");
    }

    @NewChain
    public MessageFetcher senderId(IdOnlyFetchType idOnlyFetchType) {
        return add("senderId", idOnlyFetchType);
    }

    @NewChain
    public MessageFetcher recipient() {
        return add("recipient");
    }

    @NewChain
    public MessageFetcher recipient(boolean enabled) {
        return enabled ? add("recipient") : remove("recipient");
    }

    @NewChain
    public MessageFetcher recipient(Fetcher<User> childFetcher) {
        return add("recipient", childFetcher);
    }

    @NewChain
    public MessageFetcher recipient(Fetcher<User> childFetcher,
            Consumer<FieldConfig<User, UserTable>> fieldConfig) {
        return add("recipient", childFetcher, fieldConfig);
    }

    @NewChain
    public MessageFetcher recipient(IdOnlyFetchType idOnlyFetchType) {
        return add("recipient", idOnlyFetchType);
    }

    @NewChain
    public MessageFetcher recipientId() {
        return add("recipientId");
    }

    @NewChain
    public MessageFetcher recipientId(boolean enabled) {
        return enabled ? add("recipientId") : remove("recipientId");
    }

    @NewChain
    public MessageFetcher recipientId(IdOnlyFetchType idOnlyFetchType) {
        return add("recipientId", idOnlyFetchType);
    }

    @NewChain
    public MessageFetcher content() {
        return add("content");
    }

    @NewChain
    public MessageFetcher content(boolean enabled) {
        return enabled ? add("content") : remove("content");
    }

    @NewChain
    public MessageFetcher images() {
        return add("images");
    }

    @NewChain
    public MessageFetcher images(boolean enabled) {
        return enabled ? add("images") : remove("images");
    }

    @Override
    protected MessageFetcher createFetcher(ImmutableProp prop, boolean negative,
            IdOnlyFetchType idOnlyFetchType) {
        return new MessageFetcher(this, prop, negative, idOnlyFetchType);
    }

    @Override
    protected MessageFetcher createFetcher(ImmutableProp prop,
            FieldConfig<?, ? extends Table<?>> fieldConfig) {
        return new MessageFetcher(this, prop, fieldConfig);
    }
}
