package com.celeste.entity;

import java.lang.Override;
import java.util.function.Consumer;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.lang.NewChain;
import org.babyfish.jimmer.meta.ImmutableProp;
import org.babyfish.jimmer.sql.ast.table.Table;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.babyfish.jimmer.sql.fetcher.FieldConfig;
import org.babyfish.jimmer.sql.fetcher.IdOnlyFetchType;
import org.babyfish.jimmer.sql.fetcher.impl.FetcherImpl;
import org.babyfish.jimmer.sql.fetcher.spi.AbstractTypedFetcher;

@GeneratedBy(
        type = CommentLike.class
)
public class CommentLikeFetcher extends AbstractTypedFetcher<CommentLike, CommentLikeFetcher> {
    public static final CommentLikeFetcher $ = new CommentLikeFetcher(null);

    private CommentLikeFetcher(FetcherImpl<CommentLike> base) {
        super(CommentLike.class, base);
    }

    private CommentLikeFetcher(CommentLikeFetcher prev, ImmutableProp prop, boolean negative,
            IdOnlyFetchType idOnlyFetchType) {
        super(prev, prop, negative, idOnlyFetchType);
    }

    private CommentLikeFetcher(CommentLikeFetcher prev, ImmutableProp prop,
            FieldConfig<?, ? extends Table<?>> fieldConfig) {
        super(prev, prop, fieldConfig);
    }

    public static CommentLikeFetcher $from(Fetcher<CommentLike> base) {
        return base instanceof CommentLikeFetcher ? 
        	(CommentLikeFetcher)base : 
        	new CommentLikeFetcher((FetcherImpl<CommentLike>)base);
    }

    @NewChain
    public CommentLikeFetcher user() {
        return add("user");
    }

    @NewChain
    public CommentLikeFetcher user(boolean enabled) {
        return enabled ? add("user") : remove("user");
    }

    @NewChain
    public CommentLikeFetcher user(Fetcher<User> childFetcher) {
        return add("user", childFetcher);
    }

    @NewChain
    public CommentLikeFetcher user(Fetcher<User> childFetcher,
            Consumer<FieldConfig<User, UserTable>> fieldConfig) {
        return add("user", childFetcher, fieldConfig);
    }

    @NewChain
    public CommentLikeFetcher user(IdOnlyFetchType idOnlyFetchType) {
        return add("user", idOnlyFetchType);
    }

    @NewChain
    public CommentLikeFetcher comment() {
        return add("comment");
    }

    @NewChain
    public CommentLikeFetcher comment(boolean enabled) {
        return enabled ? add("comment") : remove("comment");
    }

    @NewChain
    public CommentLikeFetcher comment(Fetcher<Comment> childFetcher) {
        return add("comment", childFetcher);
    }

    @NewChain
    public CommentLikeFetcher comment(Fetcher<Comment> childFetcher,
            Consumer<FieldConfig<Comment, CommentTable>> fieldConfig) {
        return add("comment", childFetcher, fieldConfig);
    }

    @NewChain
    public CommentLikeFetcher comment(IdOnlyFetchType idOnlyFetchType) {
        return add("comment", idOnlyFetchType);
    }

    @Override
    protected CommentLikeFetcher createFetcher(ImmutableProp prop, boolean negative,
            IdOnlyFetchType idOnlyFetchType) {
        return new CommentLikeFetcher(this, prop, negative, idOnlyFetchType);
    }

    @Override
    protected CommentLikeFetcher createFetcher(ImmutableProp prop,
            FieldConfig<?, ? extends Table<?>> fieldConfig) {
        return new CommentLikeFetcher(this, prop, fieldConfig);
    }
}
