package com.celeste.entity;

import java.lang.Deprecated;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import java.util.Date;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.sql.JoinType;
import org.babyfish.jimmer.sql.ast.PropExpression;
import org.babyfish.jimmer.sql.ast.impl.table.TableImplementor;
import org.babyfish.jimmer.sql.ast.table.TableEx;
import org.babyfish.jimmer.sql.ast.table.spi.AbstractTypedTable;

@GeneratedBy(
        type = Message.class
)
public class MessageTable extends AbstractTypedTable<Message> implements MessageProps {
    public static final MessageTable $ = new MessageTable();

    public MessageTable() {
        super(Message.class);
    }

    public MessageTable(AbstractTypedTable.DelayedOperation<Message> delayedOperation) {
        super(Message.class, delayedOperation);
    }

    public MessageTable(TableImplementor<Message> table) {
        super(table);
    }

    protected MessageTable(MessageTable base, String joinDisabledReason) {
        super(base, joinDisabledReason);
    }

    @Override
    public PropExpression.Num<Long> id() {
        return __get(MessageProps.ID.unwrap());
    }

    @Override
    public PropExpression.Cmp<Date> createTime() {
        return __get(MessageProps.CREATE_TIME.unwrap());
    }

    @Override
    public PropExpression.Cmp<Date> modifyTime() {
        return __get(MessageProps.MODIFY_TIME.unwrap());
    }

    @Override
    public UserTable sender() {
        __beforeJoin();
        if (raw != null) {
            return new UserTable(raw.joinImplementor(MessageProps.SENDER.unwrap()));
        }
        return new UserTable(joinOperation(MessageProps.SENDER.unwrap()));
    }

    @Override
    public UserTable sender(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new UserTable(raw.joinImplementor(MessageProps.SENDER.unwrap(), joinType));
        }
        return new UserTable(joinOperation(MessageProps.SENDER.unwrap(), joinType));
    }

    @Override
    public PropExpression.Num<Long> senderId() {
        return __getAssociatedId(MessageProps.SENDER.unwrap());
    }

    @Override
    public UserTable recipient() {
        __beforeJoin();
        if (raw != null) {
            return new UserTable(raw.joinImplementor(MessageProps.RECIPIENT.unwrap()));
        }
        return new UserTable(joinOperation(MessageProps.RECIPIENT.unwrap()));
    }

    @Override
    public UserTable recipient(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new UserTable(raw.joinImplementor(MessageProps.RECIPIENT.unwrap(), joinType));
        }
        return new UserTable(joinOperation(MessageProps.RECIPIENT.unwrap(), joinType));
    }

    @Override
    public PropExpression.Num<Long> recipientId() {
        return __getAssociatedId(MessageProps.RECIPIENT.unwrap());
    }

    @Override
    public PropExpression.Str content() {
        return __get(MessageProps.CONTENT.unwrap());
    }

    @Override
    public PropExpression.Str images() {
        return __get(MessageProps.IMAGES.unwrap());
    }

    @Override
    public MessageTableEx asTableEx() {
        return new MessageTableEx(this, null);
    }

    @Override
    public MessageTable __disableJoin(String reason) {
        return new MessageTable(this, reason);
    }

    @GeneratedBy(
            type = Message.class
    )
    public static class Remote extends AbstractTypedTable<Message> {
        public Remote(AbstractTypedTable.DelayedOperation delayedOperation) {
            super(Message.class, delayedOperation);
        }

        public Remote(TableImplementor<Message> table) {
            super(table);
        }

        public PropExpression.Num<Long> id() {
            return __get(MessageProps.ID.unwrap());
        }

        @Override
        @Deprecated
        public TableEx<Message> asTableEx() {
            throw new UnsupportedOperationException();
        }

        @Override
        public Remote __disableJoin(String reason) {
            return this;
        }
    }
}
