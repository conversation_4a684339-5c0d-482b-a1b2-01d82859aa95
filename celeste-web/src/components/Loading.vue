<script setup>
import loading from '@/assets/lottie/loading.json'

defineProps(['loading'])
</script>

<template>
    <div class="relative size-full">
        <ProgressSpinner
            v-if="loading"
            strokeWidth="8"
            fill="transparent"
            animationDuration="0.5s"
            class="absolute !top-1/2 left-1/2 !size-16 -translate-x-1/2 -translate-y-1/2" />
        <slot v-else />
    </div>
</template>

<style scoped></style>
