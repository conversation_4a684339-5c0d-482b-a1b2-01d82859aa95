<script setup>
import { toast } from '@/components/toast/index.js'
import VideoCard from '@/components/VideoCard.vue'
import { ref } from 'vue'

const props = defineProps(['video'])

const menuRef = ref()

const items = ref([
    {
        label: '编辑',
        icon: 'icon-[prime--pen-to-square]',
        command: () => toast.success('编辑成功')
    },
    { label: '数据', icon: 'icon-[prime--chart-scatter]' },
    { label: '删除', icon: 'icon-[prime--trash]' }
])

const style = {
    '--p-contextmenu-item-color': 'gray',
    '--p-contextmenu-item-focus-color': 'gray',
    '--p-contextmenu-item-focus-background': 'rgb(186 230 253)'
}

const open = (e) => menuRef.value.show(e)
</script>

<template>
    <div>
        <VideoCard :video="video" @contextmenu="open" />
        <ContextMenu :id="String(video.id)" ref="menuRef" :model="items" :style="style" />
    </div>
</template>

<style scoped></style>
