<template>
    <div class="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-md max-h-96 overflow-y-auto z-50">
        <div class="text-sm font-bold mb-2">聊天调试信息</div>
        
        <div class="text-xs space-y-2">
            <div><strong>当前用户ID:</strong> {{ userStore.user?.id }}</div>
            <div><strong>当前聊天对象:</strong> {{ chatStore.currentChat?.id }} ({{ chatStore.currentChat?.username }})</div>
            <div><strong>消息数量:</strong> {{ currentMessages.length }}</div>
            
            <div class="border-t pt-2">
                <div class="font-semibold">消息列表:</div>
                <div v-for="msg in currentMessages" :key="msg.id" class="border-b py-1 text-xs">
                    <div><strong>ID:</strong> {{ msg.id }}</div>
                    <div><strong>发送者:</strong> {{ msg.senderId }}</div>
                    <div><strong>接收者:</strong> {{ msg.recipientId }}</div>
                    <div><strong>内容:</strong> {{ msg.content }}</div>
                    <div><strong>右侧显示:</strong> {{ msg.senderId === userStore.user?.id ? '是' : '否' }}</div>
                    <div><strong>待发送:</strong> {{ msg.pending ? '是' : '否' }}</div>
                </div>
            </div>
        </div>
        
        <button @click="$emit('close')" class="mt-2 px-2 py-1 bg-gray-200 rounded text-xs">关闭</button>
    </div>
</template>

<script setup>
import { computed } from 'vue'
import { useChatStore } from '@/stores/model/chat'
import { useUserStore } from '@/stores/model/user'

const chatStore = useChatStore()
const userStore = useUserStore()

const currentMessages = computed(() => {
    if (!chatStore.currentChat) return []
    const chatKey = chatStore.getChatKey(chatStore.currentChat.id)
    return chatStore.messagesByChat[chatKey] || []
})

defineEmits(['close'])
</script>
