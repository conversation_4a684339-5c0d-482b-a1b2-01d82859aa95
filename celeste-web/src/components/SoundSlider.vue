<script setup>
import ButtonIcon from '@/components/ButtonIcon.vue'
import { useVideoStore } from '@/stores/model/video.js'
import { eventBus } from '@/utils/eventBus.js'
import { $dt } from '@primevue/themes'

import { ref } from 'vue'

const videoStore = useVideoStore()

const hidden = ref(true)
const { value } = $dt('button.raised.shadow')
const sound = ref(videoStore.getSound() * 100)

const setSound = (newSound) => {
    videoStore.sound = newSound / 100
    eventBus.emit('sound', newSound / 100)
}
</script>

<template>
    <div class="relative mb-5">
        <div
            :class="hidden ? 'invisible' : 'visible'"
            class="absolute bottom-12 flex w-full justify-center rounded-full py-5"
            :style="{ 'box-shadow': value }">
            <Slider v-model="sound" orientation="vertical" @update:modelValue="setSound" class="h-32" />
        </div>
        <ButtonIcon
            :icon="[sound === 0 ? 'icon-[charm--sound-mute]' : 'icon-[charm--sound-up]', 'text-xl']"
            @click="hidden = !hidden" />
    </div>
</template>

<style scoped></style>
