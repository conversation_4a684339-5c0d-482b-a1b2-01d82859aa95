<script setup>
import { adoreUser<PERSON>pi, unAdoreUserApi } from '@/api/userApi.js'
import { addVideoLikeApi, removeVideoLikeApi, unCollectVideoApi } from '@/api/videoApi.js'
import AsideIcon from '@/components/AsideIcon.vue'
import CollectVideo from '@/components/dialog/CollectVideo.vue'
import Login from '@/components/dialog/Login.vue'
import VideoSidebar from '@/components/VideoSidebar.vue'
import { useUserStore } from '@/stores/model/user.js'
import { useVideoStore } from '@/stores/model/video.js'
import { eventBus } from '@/utils/eventBus.js'
import { useDialog } from 'primevue/usedialog'
import { computed, onMounted, onUnmounted, provide, ref } from 'vue'
import { useRouter } from 'vue-router'
import Player from 'xgplayer'
import 'xgplayer/dist/index.min.css'

const props = defineProps(['video', 'active'])

provide('activeVideo', props.video)
provide('isActive', computed(() => props.active)) // prettier-ignore

const dialog = useDialog()
const router = useRouter()
const userStore = useUserStore()
const videoStore = useVideoStore()

const player = ref()
const playerRef = ref()

const adore = () => {
    if (userStore.isLogin) {
        if (props.video.userIsAdore) unAdoreUserApi(props.video.user.id).then(() => (props.video.userIsAdore = false))
        else adoreUserApi(props.video.user.id).then(() => (props.video.userIsAdore = true))
    } else dialog.open(Login)
}

const like = () => {
    if (userStore.isLogin) {
        if (props.video.userIsLike) {
            removeVideoLikeApi(props.video.id).then(() => {
                props.video.likesSum--
                props.video.userIsLike = false
            })
        } else {
            addVideoLikeApi(props.video.id).then(() => {
                props.video.likesSum++
                props.video.userIsLike = true
            })
        }
    } else dialog.open(Login)
}

const comment = () => {
    videoStore.sidebar.open = true
    videoStore.sidebar.tabs = 'comments'
}

const collection = () => {
    if (userStore.isLogin) {
        if (props.video.userIsCollect) {
            unCollectVideoApi(props.video.id).then(() => {
                props.video.collectionsSum--
                props.video.userIsCollect = false
            })
        } else {
            dialog.open(CollectVideo, {
                data: props.video.id,
                onClose: ({ data }) => {
                    if (data) {
                        props.video.collectionsSum++
                        props.video.userIsCollect = true
                    }
                }
            })
        }
    } else dialog.open(Login)
}

const seekVideoTime = (e) => {
    if (!props.active) return
    if (e.key === 'ArrowLeft') player.value.currentTime = player.value.currentTime - 5
    if (e.key === 'ArrowRight') player.value.currentTime = player.value.currentTime + 5
}

const setVideoSound = (e) => (player.value.volume = e)

const setVideoState = () => (props.active ? player.value.reload() : player.value.reset())

onMounted(() => {
    player.value = new Player({
        url: props.video.source,
        height: '100%',
        el: playerRef.value,
        volume: videoStore.getSound(),
        loop: true,
        closeVideoDblclick: true,
        enableContextmenu: false,
        controls: { mode: 'bottom' },
        dynamicBg: { disable: false },
        autoplay: props.active,
        start: { isShowPause: true, disableAnimate: true },
        commonStyle: { playedColor: '#0ea5e9', cachedColor: '#bae6fd' },
        ignores: ['enter', 'keyboard', 'play', 'time', 'volume', 'fullscreen', 'cssfullscreen', 'playbackrate']
    })
    eventBus.on('state', setVideoState)
    eventBus.on('sound', setVideoSound)
    eventBus.on('play', () => player.value.pause())
    document.addEventListener('keyup', seekVideoTime)
})

onUnmounted(() => {
    eventBus.off('state', setVideoState)
    eventBus.off('sound', setVideoSound)
    document.removeEventListener('keyup', seekVideoTime)
})
</script>

<template>
    <div class="flex size-full">
        <div ref="playerRef" class="relative grow">
            <div class="absolute bottom-1/2 right-5 flex translate-y-1/2 flex-col items-center justify-center gap-10">
                <div class="relative flex items-center justify-center">
                    <Avatar
                        :image="`http://localhost:8080/api/user/getAvatar/${video.user.username}`"
                        @click="router.push(`/user/${video.user.id}`)"
                        class="!h-10 !w-10 cursor-pointer"
                        shape="circle" />
                    <i
                        @click="adore"
                        :class="[video.userIsAdore ? 'icon-[gravity-ui--circle-check-fill]' : 'icon-[gravity-ui--circle-plus-fill]']"
                        class="absolute -bottom-3 text-xl text-red-500" />
                </div>
                <AsideIcon
                    :label="video.likesSum"
                    icon="icon-[flowbite--heart-solid]"
                    @click="like"
                    :class="[video.userIsLike ? 'text-red-500' : 'text-white']" />
                <AsideIcon :label="video.commentsSum" icon="icon-[flowbite--message-dots-solid] text-white" @click="comment" />
                <AsideIcon
                    :label="video.collectionsSum"
                    icon="icon-[flowbite--star-solid]"
                    @click="collection"
                    :class="[video.userIsCollect ? 'text-yellow-500' : 'text-white']" />
            </div>
            <div class="absolute bottom-5 left-5 text-white">
                <p class="text-lg font-bold">
                    @{{ video.user.username }}
                    <span class="text-xs font-normal">· {{ video.betweenTime }}</span>
                </p>
                <p>{{ video.description }}</p>
            </div>
        </div>
        <VideoSidebar v-show="videoStore.sidebar.open" :video="video" @wheel.stop class="basis-[30%] rounded-r-2xl" />
    </div>
</template>

<style scoped></style>
