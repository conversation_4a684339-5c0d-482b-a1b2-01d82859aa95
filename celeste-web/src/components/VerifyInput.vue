<script setup>
import { useField } from 'vee-validate'
import { ref } from 'vue'

const props = defineProps(['icon', 'name', 'form', 'password', 'placeholder'])

const security = ref(true)
const { value, errorMessage } = useField(props.name, null, { form: props.form })
</script>

<template>
    <div>
        <div class="relative flex items-center">
            <i :class="icon" class="absolute left-0 ml-2" />
            <InputText
                v-model="value"
                size="small"
                :placeholder="placeholder"
                :invalid="errorMessage != null"
                :type="password && security ? 'password' : 'text'"
                :class="{ '!pr-8': password }"
                class="w-full !pl-8" />
            <i
                v-if="password"
                @click="security = !security"
                :class="security ? 'icon-[prime--eye] text-xl' : 'icon-[prime--eye-slash] text-xl'"
                class="absolute right-0 mr-2 cursor-pointer" />
        </div>
        <small class="absolute text-red-400">{{ errorMessage }}</small>
    </div>
</template>

<style scoped>
input::-ms-reveal {
    display: none;
}
</style>
