<script setup>
import { postCommentApi } from '@/api/commentApi.js'
import { useCommentStore } from '@/stores/model/comment.js'
import { eventBus } from '@/utils/eventBus.js'
import { useIntersectionObserver } from '@vueuse/core'
import { NPopover } from 'naive-ui'
import { inject, onUnmounted, ref } from 'vue'

const isActive = inject('isActive')
const activeVideo = inject('activeVideo')

const commentStore = useCommentStore()

const text = ref()
const inputRef = ref()
const popover = ref(false)

const send = () => {
    if (popover.value) {
        popover.value = false
        commentStore.comment.content = text.value
        postCommentApi({
            ...commentStore.comment,
            videoId: activeVideo.id
        }).then(() => eventBus.emit('showChildComment'))
    } else {
        postCommentApi({
            content: text.value,
            videoId: activeVideo.id
        }).then(() => eventBus.emit('showRootComment'))
    }
    text.value = ''
}

const { stop } = useIntersectionObserver(inputRef, ([{ isIntersecting }]) => {
    if (!isIntersecting) popover.value = false
})

const setPopover = () => (popover.value = isActive.value)

eventBus.on('popover', setPopover)

onUnmounted(() => {
    stop()
    eventBus.off('popover', setPopover)
})
</script>

<template>
    <InputGroup class="absolute bottom-2 h-10 !w-[calc(100%-2.5rem)]">
        <NPopover :show="popover" trigger="manual" :theme-overrides="{ padding: '0 0.5rem', borderRadius: '1rem' }">
            <template #trigger>
                <InputText v-model="text" ref="inputRef" placeholder="请留下你的精彩评论吧..." />
            </template>
            <div class="flex h-8 w-72 items-center justify-between text-gray-500">
                <p class="line-clamp-1 text-xs">@{{ commentStore.comment.username }}：{{ commentStore.comment.content }}</p>
                <i @click="popover = false" class="icon-[prime--times-circle] shrink-0 cursor-pointer text-xl" />
            </div>
        </NPopover>
        <InputGroupAddon @click="send" class="cursor-pointer">
            <i class="icon-[prime--send] text-xl" />
        </InputGroupAddon>
    </InputGroup>
</template>

<style scoped></style>
