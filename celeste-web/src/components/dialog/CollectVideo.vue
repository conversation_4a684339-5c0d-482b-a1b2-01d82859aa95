<script setup>
import { createCollectionApi, getMyCollectionsApi } from '@/api/userApi.js'
import { collectVideoApi } from '@/api/videoApi.js'
import { toast } from '@/components/toast/index.js'
import { NInfiniteScroll } from 'naive-ui'
import { inject, onBeforeMount, ref } from 'vue'

const dialogRef = inject('dialogRef')

const collections = ref([])
const collectionsIds = ref([])
const newCollectionName = ref()

const newCollection = () => {
    createCollectionApi(newCollectionName.value).then((data) => {
        newCollectionName.value = ''
        collections.value.unshift({ ...data, videosSum: 0 })
    })
}

const click = () => {
    if (collectionsIds.value.length) collectVideoApi(dialogRef.value.data, collectionsIds.value).then(() => dialogRef.value.close(true))
    else toast.warning('请至少选择一个收藏夹')
}

onBeforeMount(() => getMyCollectionsApi().then((data) => collections.value.push(...data)))
</script>

<template>
    <div class="flex h-80 w-72 flex-col gap-5 p-5">
        <NInfiniteScroll :scrollbar-props="{ contentClass: 'flex flex-col gap-5', themeOverrides: { width: '0' } }">
            <div v-for="collection in collections" class="flex justify-between text-gray-500">
                <div class="flex items-center gap-5">
                    <Checkbox v-model="collectionsIds" :value="collection.id" />
                    <span>{{ collection.name }}</span>
                </div>
                <span class="">{{ collection.videosSum }}</span>
            </div>
            <InputGroup class="">
                <InputText v-model="newCollectionName" placeholder="新建收藏夹" />
                <InputGroupAddon @click="newCollection" class="cursor-pointer">
                    <i class="icon-[prime--plus] text-xl" />
                </InputGroupAddon>
            </InputGroup>
        </NInfiniteScroll>
        <div class="flex justify-between gap-5">
            <Button label="取消" @click="dialogRef.close()" class="basis-1/2" />
            <Button label="确认" @click="click" class="basis-1/2" />
        </div>
    </div>
</template>

<style scoped></style>
