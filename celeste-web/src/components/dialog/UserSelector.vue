<script setup>
import { ref, onMounted, computed } from 'vue'
import { searchUsersApi, getAllUsersApi } from '@/api/userApi.js'
import { NModal, NCard, NInput, NList, NListItem, NAvatar, NButton, NSpin, NEmpty } from 'naive-ui'

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['update:show', 'select-user'])

const searchKeyword = ref('')
const userList = ref([])
const loading = ref(false)
const page = ref(0)
const hasMore = ref(true)

// 计算属性
const showModal = computed({
    get: () => props.show,
    set: (value) => emit('update:show', value)
})

// 获取用户头像URL
const getUserAvatar = (username) => {
    return `/api/user/getAvatar/${username}`
}

// 搜索用户
const searchUsers = async (reset = false) => {
    if (loading.value) return
    
    loading.value = true
    
    try {
        if (reset) {
            page.value = 0
            userList.value = []
        }
        
        const response = searchKeyword.value.trim() 
            ? await searchUsersApi(searchKeyword.value, page.value, 20)
            : await getAllUsersApi(page.value, 20)
        
        if (reset) {
            userList.value = response.data || response
        } else {
            userList.value.push(...(response.data || response))
        }
        
        hasMore.value = (response.data || response).length === 20
        page.value++
    } catch (error) {
        console.error('搜索用户失败:', error)
    } finally {
        loading.value = false
    }
}

// 处理搜索输入
const handleSearch = () => {
    searchUsers(true)
}

// 选择用户
const selectUser = (user) => {
    emit('select-user', user)
    showModal.value = false
}

// 加载更多
const loadMore = () => {
    if (hasMore.value && !loading.value) {
        searchUsers(false)
    }
}

// 初始化
onMounted(() => {
    if (props.show) {
        searchUsers(true)
    }
})

// 监听弹窗显示状态
const handleModalShow = () => {
    if (props.show) {
        searchKeyword.value = ''
        searchUsers(true)
    }
}
</script>

<template>
    <NModal 
        v-model:show="showModal" 
        preset="card"
        title="选择聊天对象"
        size="medium"
        :mask-closable="true"
        @after-enter="handleModalShow"
    >
        <div class="user-selector">
            <!-- 搜索框 -->
            <div class="mb-4">
                <NInput
                    v-model:value="searchKeyword"
                    placeholder="搜索用户名或邮箱..."
                    clearable
                    @keyup.enter="handleSearch"
                    @clear="handleSearch"
                >
                    <template #suffix>
                        <NButton 
                            text 
                            @click="handleSearch"
                            :loading="loading"
                        >
                            🔍
                        </NButton>
                    </template>
                </NInput>
            </div>

            <!-- 用户列表 -->
            <div class="user-list-container">
                <NSpin :show="loading && userList.length === 0">
                    <div v-if="userList.length === 0 && !loading" class="text-center py-8">
                        <NEmpty description="暂无用户" />
                    </div>
                    
                    <NList v-else hoverable clickable>
                        <NListItem
                            v-for="user in userList"
                            :key="user.id"
                            @click="selectUser(user)"
                            class="cursor-pointer hover:bg-gray-50 transition-colors"
                        >
                            <div class="flex items-center space-x-3 p-2">
                                <NAvatar
                                    :src="getUserAvatar(user.username)"
                                    size="medium"
                                    fallback-src="/default-avatar.svg"
                                />
                                <div class="flex-1 min-w-0">
                                    <div class="font-medium text-gray-900 truncate">
                                        {{ user.username }}
                                    </div>
                                    <div class="text-sm text-gray-500 truncate">
                                        {{ user.email }}
                                    </div>
                                </div>
                                <div class="text-blue-500">
                                    💬
                                </div>
                            </div>
                        </NListItem>
                    </NList>
                </NSpin>

                <!-- 加载更多按钮 -->
                <div v-if="hasMore && userList.length > 0" class="text-center mt-4">
                    <NButton 
                        @click="loadMore" 
                        :loading="loading"
                        text
                        type="primary"
                    >
                        加载更多
                    </NButton>
                </div>
            </div>
        </div>
    </NModal>
</template>

<style scoped>
.user-selector {
    max-height: 500px;
    display: flex;
    flex-direction: column;
}

.user-list-container {
    flex: 1;
    overflow-y: auto;
    min-height: 300px;
}

/* 自定义滚动条样式 */
.user-list-container::-webkit-scrollbar {
    width: 6px;
}

.user-list-container::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.user-list-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.user-list-container::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}
</style>
