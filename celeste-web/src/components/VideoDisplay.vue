<script setup>
import { demandApi } from '@/api/videoApi.js'
import VideoPlayer from '@/components/VideoPlayer.vue'
import { eventBus } from '@/utils/eventBus.js'
import { reactive, ref } from 'vue'

const visible = ref(false)
const video = reactive({})

const close = () => (visible.value = false)

eventBus.on('play', (e) => {
    demandApi(e).then((data) => {
        visible.value = true
        Object.assign(video, data)
    })
})
</script>

<template>
    <Drawer :visible="visible" position="full">
        <template #container>
            <div class="flex size-full bg-[url(@/assets/image/background.png)] bg-cover">
                <div class="absolute left-5 top-5 z-10 flex cursor-pointer rounded-full bg-white p-2 hover:bg-sky-300" @click="close">
                    <i class="icon-[prime--times] text-xl" />
                </div>
                <VideoPlayer :key="video.id" :video="video" :active="true" />
            </div>
        </template>
    </Drawer>
</template>

<style scoped></style>
