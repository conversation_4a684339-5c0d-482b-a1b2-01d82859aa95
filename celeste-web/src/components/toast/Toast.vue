<script setup>
import error from '@/assets/lottie/error.json'
import info from '@/assets/lottie/info.json'
import success from '@/assets/lottie/success.json'
import warning from '@/assets/lottie/warning.json'
import { eventBus } from '@/utils/eventBus.js'

import { ref } from 'vue'
import { Vue3Lottie } from 'vue3-lottie'

const key = ref(0)
const text = ref('')
const queue = ref([])
const icon = ref(null)
const show = ref(false)
const icons = { info, success, warning, error }

const next = () => {
    if (queue.value.length > 0) {
        key.value++
        show.value = true
        const { Type, Text } = queue.value.shift()
        icon.value = icons[Type]
        text.value = Text
        setTimeout(() => (show.value = false), 1500)
    }
}

eventBus.on('toast', (toast) => {
    queue.value.push(toast)
    if (!show.value) next()
})
</script>

<template>
    <Teleport to="body">
        <div class="fixed right-1/2 top-0 z-[9999] translate-x-1/2">
            <Transition
                @after-leave="next"
                enter-active-class="animate-fadeinup animate-duration-500"
                leave-to-class="animate-fadeoutup animate-duration-500">
                <div
                    v-show="show"
                    class="flex w-screen items-center justify-center gap-2 rounded-lg bg-[#F5F5F5FF] p-5 font-black shadow-lg min-[450px]:mt-5 min-[450px]:w-fit">
                    <Vue3Lottie
                        noMargin
                        width="1.5rem"
                        height="1.5rem"
                        :key="key"
                        :loop="false"
                        :animationData="icon" />
                    <p>{{ text }}</p>
                </div>
            </Transition>
        </div>
    </Teleport>
</template>

<style scoped></style>
