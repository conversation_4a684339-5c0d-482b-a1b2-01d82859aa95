<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
    src: {
        type: String,
        required: true
    },
    alt: {
        type: String,
        default: '图片'
    },
    preview: {
        type: Boolean,
        default: false
    },
    lazy: {
        type: Boolean,
        default: true
    },
    placeholder: {
        type: String,
        default: '/placeholder-image.png'
    },
    errorImage: {
        type: String,
        default: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzIiIGZpbGw9IiNGM0Y0RjYiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyNCIgcj0iMTAiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTEyIDUyQzEyIDQzLjE2MzQgMTkuMTYzNCAzNiAyOCAzNkg0NEM1Mi44MzY2IDM2IDYwIDQzLjE2MzQgNjAgNTJWNjRIMTJWNTJaIiBmaWxsPSIjOUNBM0FGIi8+Cjwvc3ZnPgo='
    }
})

const emit = defineEmits(['load', 'error', 'click'])

const imageRef = ref(null)
const isLoading = ref(true)
const hasError = ref(false)
const showPreview = ref(false)

// 计算实际显示的图片源
const displaySrc = computed(() => {
    if (hasError.value) {
        return props.errorImage
    }
    if (isLoading.value && props.lazy) {
        return props.placeholder
    }
    return props.src
})

// 处理图片加载完成
const handleLoad = () => {
    isLoading.value = false
    hasError.value = false
    emit('load')
}

// 处理图片加载错误
const handleError = (event) => {
    console.log('图片加载失败:', props.src)
    isLoading.value = false
    hasError.value = true

    // 如果是头像加载失败，尝试设置默认头像
    if (event && event.target && props.src.includes('/getAvatar/')) {
        event.target.src = props.errorImage
    }

    emit('error', event)
}

// 处理图片点击
const handleClick = () => {
    if (props.preview) {
        showPreview.value = true
    }
    emit('click')
}

// 关闭预览
const closePreview = () => {
    showPreview.value = false
}

// 阻止预览弹窗的点击事件冒泡
const stopPropagation = (event) => {
    event.stopPropagation()
}
</script>

<template>
    <div class="relative inline-block">
        <!-- 主图片 -->
        <img
            ref="imageRef"
            :src="displaySrc"
            :alt="alt"
            :class="[
                'transition-opacity duration-300',
                {
                    'opacity-50': isLoading,
                    'cursor-pointer': preview,
                    'hover:opacity-90': preview
                }
            ]"
            @load="handleLoad"
            @error="handleError"
            @click="handleClick"
            v-bind="$attrs"
        />

        <!-- 加载指示器 -->
        <div
            v-if="isLoading"
            class="absolute inset-0 flex items-center justify-center bg-gray-100 rounded"
        >
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
        </div>

        <!-- 错误指示器 -->
        <div
            v-if="hasError"
            class="absolute inset-0 flex items-center justify-center bg-gray-100 rounded text-gray-400"
        >
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
        </div>

        <!-- 预览模态框 -->
        <Teleport to="body">
            <div
                v-if="showPreview && preview"
                class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75"
                @click="closePreview"
            >
                <div class="relative max-w-4xl max-h-4xl p-4">
                    <!-- 关闭按钮 -->
                    <button
                        @click="closePreview"
                        class="absolute top-2 right-2 z-10 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-all"
                    >
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>

                    <!-- 预览图片 -->
                    <img
                        :src="src"
                        :alt="alt"
                        class="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
                        @click="stopPropagation"
                    />
                </div>
            </div>
        </Teleport>
    </div>
</template>

<style scoped>
/* 确保图片不会超出容器 */
img {
    max-width: 100%;
    height: auto;
}

/* 预览动画 */
.fixed {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 加载动画 */
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}
</style>
