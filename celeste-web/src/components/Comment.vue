<script setup>
import { getChildComments<PERSON>pi, getLastCommentApi } from '@/api/commentApi.js'
import loadingData from '@/assets/lottie/loading.json'
import Login from '@/components/dialog/Login.vue'
import { useCommentStore } from '@/stores/model/comment.js'
import { useUserStore } from '@/stores/model/user.js'
import { eventBus } from '@/utils/eventBus.js'
import { useDialog } from 'primevue/usedialog'
import { onUnmounted, reactive, ref } from 'vue'
import { Vue3Lottie } from 'vue3-lottie'

const props = defineProps(['rootComment'])

const dialog = useDialog()
const userStore = useUserStore()
const commentStore = useCommentStore()

const open = ref(false)
const loading = ref(false)
const childComments = reactive({
    page: 0,
    rows: [],
    pageCount: null
})

const replyRoot = (comment) => {
    if (userStore.isLogin) {
        eventBus.emit('popover')
        commentStore.comment.rootId = comment.id
        commentStore.comment.content = comment.content
        commentStore.comment.username = comment.user.username
    } else dialog.open(Login)
}

const replyChild = (comment) => {
    if (userStore.isLogin) {
        eventBus.emit('popover')
        commentStore.comment.parentId = comment.id
        commentStore.comment.content = comment.content
        commentStore.comment.rootId = props.rootComment.id
        commentStore.comment.username = comment.user.username
    } else dialog.open(Login)
}

const like = (comment) => {
    if (userStore.isLogin) {
        console.log(comment)
    } else dialog.open(Login)
}

const more = (flag) => {
    open.value = true
    if (!childComments.rows.length || flag) {
        loading.value = true
        getChildCommentsApi({ rootId: props.rootComment.id, page: childComments.page++, size: 5 }).then((data) => {
            setTimeout(() => {
                loading.value = false
                childComments.rows.push(...data.rows)
                childComments.pageCount = data.totalPageCount
            }, 500)
        })
    }
}

const showComment = () => {
    if (props.rootComment.id === commentStore.comment.rootId) {
        getLastCommentApi().then((data) => {
            open.value = true
            childComments.rows.unshift(data)
        })
    }
}

eventBus.on('showChildComment', showComment)

onUnmounted(() => eventBus.off('showChildComment', showComment))
</script>

<template>
    <div class="my-4 flex gap-3 first:mt-0 last:mb-0">
        <Avatar
            shape="circle"
            :image="`http://localhost:8080/api/user/getAvatar/${rootComment.user.username}`"
            @click=""
            class="!size-8 shrink-0 cursor-pointer" />
        <div class="flex w-full flex-col gap-2 text-gray-500">
            <p class="text-xs">{{ rootComment.user.username }}</p>
            <p class="text-sm text-gray-700">{{ rootComment.content }}</p>
            <div class="flex items-center justify-between">
                <span class="text-xs">{{ rootComment.betweenTime }}</span>
                <div class="flex gap-3">
                    <div @click="replyRoot(rootComment)" class="flex cursor-pointer items-center gap-1">
                        <i class="icon-[prime--comment] text-xl" />
                        <span class="text-xs">回复</span>
                    </div>
                    <div @click="like(rootComment)" class="flex cursor-pointer items-center gap-1">
                        <i class="icon-[prime--thumbs-up] text-xl" />
                        <span class="text-xs">{{ rootComment.likesSum }}</span>
                    </div>
                </div>
            </div>
            <div class="flex flex-col">
                <div v-if="open">
                    <div v-for="childComment in childComments.rows" class="my-4 flex gap-3">
                        <Avatar
                            shape="circle"
                            :image="`http://localhost:8080/api/user/getAvatar/${childComment.user.username}`"
                            @click=""
                            class="!size-8 shrink-0 cursor-pointer" />
                        <div class="flex w-full flex-col gap-2 text-gray-500">
                            <div class="text-xs">
                                {{ childComment.user.username }}
                                <span v-if="childComment.parent">-> {{ childComment.parent.user.username }}</span>
                            </div>
                            <p class="text-sm text-gray-700">
                                {{ childComment.content }}
                            </p>
                            <div class="flex items-center justify-between">
                                <span class="text-xs">{{ childComment.betweenTime }}</span>
                                <div class="flex gap-3">
                                    <div @click="replyChild(childComment)" class="flex cursor-pointer items-center gap-1">
                                        <i class="icon-[prime--comment] text-xl" />
                                        <span class="text-xs">回复</span>
                                    </div>
                                    <div @click="like(childComment)" class="flex cursor-pointer items-center gap-1">
                                        <i class="icon-[prime--thumbs-up] text-xl" />
                                        <span class="text-xs">{{ childComment.likesSum }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <Vue3Lottie v-if="loading" width="3rem" height="3rem" :animationData="loadingData" class="scale-150" />
                </div>
                <div v-if="rootComment.childrenSum > 0" class="flex items-center text-xs">
                    <span>-----</span>
                    <div v-if="!open" @click="more(false)" class="flex cursor-pointer items-center">
                        <span>展开{{ rootComment.childrenSum }}条回复</span>
                        <i class="icon-[prime--angle-down] text-xl" />
                    </div>
                    <div
                        v-else-if="childComments.page < childComments.pageCount - 1"
                        @click="more(true)"
                        class="mr-5 flex cursor-pointer items-center">
                        <span>展开更多</span>
                        <i class="icon-[prime--angle-down] text-xl" />
                    </div>
                    <div v-if="open" @click="open = false" class="flex cursor-pointer items-center">
                        <span>收起</span>
                        <i class="icon-[prime--angle-up] text-xl" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped></style>
