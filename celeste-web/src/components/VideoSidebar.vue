<script setup>
import { getLastCommentApi, getRootCommentsApi } from '@/api/commentApi.js'
import { getOtherVideosApi } from '@/api/videoApi.js'
import loadingData from '@/assets/lottie/loading.json'
import Comment from '@/components/Comment.vue'
import CommentEdit from '@/components/CommentEdit.vue'
import VideoCard from '@/components/VideoCard.vue'
import { useVideoStore } from '@/stores/model/video.js'
import { eventBus } from '@/utils/eventBus.js'
import { $dt } from '@primevue/themes'
import { NInfiniteScroll, NTabPane, NTabs } from 'naive-ui'
import { inject, onBeforeMount, onUnmounted, reactive } from 'vue'
import { Vue3Lottie } from 'vue3-lottie'

const activeVideo = inject('activeVideo')

const videoStore = useVideoStore()

const authorVideos = reactive({
    page: 0,
    rows: [],
    pageCount: null,
    loading: false
})

const rootComments = reactive({
    page: 0,
    rows: [],
    pageCount: null,
    loading: false
})

const theme = {
    panePaddingLarge: '1.25rem',
    barColor: $dt('sky.500').value,
    tabTextColorHoverBar: $dt('sky.500').value,
    tabTextColorActiveBar: $dt('sky.500').value
}

const getOtherVideos = () => {
    if (!authorVideos.pageCount || authorVideos.page <= authorVideos.pageCount - 1) {
        if (!authorVideos.loading) {
            authorVideos.loading = true
            getOtherVideosApi({
                videoId: activeVideo.id,
                page: authorVideos.page++,
                size: 10
            }).then((data) => {
                setTimeout(() => {
                    authorVideos.loading = false
                    authorVideos.rows.push(...data.rows)
                    authorVideos.pageCount = data.totalPageCount
                }, 500)
            })
        }
    }
}

const getRootComments = () => {
    if (!rootComments.pageCount || rootComments.page < rootComments.pageCount - 1) {
        if (!rootComments.loading) {
            rootComments.loading = true
            getRootCommentsApi({
                videoId: activeVideo.id,
                page: rootComments.page++,
                size: 10
            }).then((data) => {
                setTimeout(() => {
                    rootComments.loading = false
                    rootComments.rows.push(...data.rows)
                    rootComments.pageCount = data.totalPageCount
                }, 500)
            })
        }
    }
}

const showComment = () => getLastCommentApi().then((data) => rootComments.rows.unshift(data))

eventBus.on('showRootComment', showComment)

onBeforeMount(() => {
    getOtherVideos()
    getRootComments()
})

onUnmounted(() => eventBus.off('showRootComment', showComment))
</script>

<template>
    <NTabs size="large" :value="videoStore.sidebar.tabs" :theme-overrides="theme" justify-content="space-evenly" class="bg-white/50">
        <NTabPane name="author" display-directive="show" class="animate-fadein animate-duration-500">
            <template #tab>
                <div @click="videoStore.sidebar.tabs = 'author'" class="flex items-center gap-1">
                    <i class="icon-[prime--user] text-xl" />
                    <span>作者</span>
                </div>
            </template>
            <NInfiniteScroll
                :distance="5"
                :on-load="getOtherVideos"
                :scrollbar-props="{ contentClass: 'flex flex-wrap gap-5', themeOverrides: { width: '0' } }"
                class="h-[calc((100vh-3.75rem)*11/12-2.5rem-44px)]">
                <VideoCard v-for="authorVideo in authorVideos.rows" :img="true" :video="authorVideo" class="h-48 w-[calc((100%-1.25rem)*1/2)]" />
                <Vue3Lottie v-if="authorVideos.loading" width="3rem" height="3rem" :animationData="loadingData" class="w-full scale-150" />
                <p v-else class="w-full text-center text-gray-500">暂时没有更多视频...</p>
            </NInfiniteScroll>
        </NTabPane>
        <NTabPane name="comments" display-directive="show" class="relative grow animate-fadein animate-duration-500">
            <template #tab>
                <div @click="videoStore.sidebar.tabs = 'comments'" class="flex items-center gap-1">
                    <i class="icon-[prime--comments] text-xl" />
                    <span>评论</span>
                </div>
            </template>
            <NInfiniteScroll
                :distance="10"
                :on-load="getRootComments"
                :scrollbar-props="{ themeOverrides: { width: '0' } }"
                class="h-[calc((100vh-3.75rem)*11/12-4.75rem-44px)]">
                <Comment v-for="rootComment in rootComments.rows" :rootComment="rootComment" />
                <Vue3Lottie v-if="rootComments.loading" width="3rem" height="3rem" :animationData="loadingData" class="scale-150" />
                <p v-if="!activeVideo.commentsSum" class="text-center text-gray-500">当前视频还没有评论...</p>
                <p v-else-if="!rootComments.loading" class="text-center text-gray-500">暂时没有更多评论...</p>
            </NInfiniteScroll>
            <CommentEdit  />
        </NTabPane>
    </NTabs>
</template>

<style scoped></style>
