<script setup>
import Tips from '@/components/dialog/Tips.vue'
import { useUserStore } from '@/stores/model/user.js'
import { useDialog } from 'primevue/usedialog'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const dialog = useDialog()
const router = useRouter()
const userStore = useUserStore()

const items = ref([
    { label: '推荐', icon: 'icon-[prime--home] text-xl', path: '/' },
    { label: '聊天', icon: 'icon-[prime--comments] text-xl', path: '/chat', requireLogin: true },
    // { label: '热门', icon: 'icon-[ph--fire] text-xl', path: '/hot' },
    // { label: '关注', icon: 'icon-[prime--users] text-xl', path: '/follow' },
    { label: '生活', icon: 'icon-[ri--magic-line] text-xl', path: '/type/1' },
    { label: '历史', icon: 'icon-[ic--sharp-history] text-xl', path: '/type/2' },
    { label: '宠物', icon: 'icon-[solar--cat-linear] text-xl', path: '/type/3' },
    { label: '风景', icon: 'icon-[lucide--image] text-xl', path: '/type/4' },
    { label: '美食', icon: 'icon-[ep--food] text-xl', path: '/type/5' },
    { label: '知识', icon: 'icon-[solar--book-broken] text-xl', path: '/type/6' },
    { label: '体育', icon: 'icon-[ic--outline-sports-baseball] text-xl', path: '/type/7' },
    { label: '游戏', icon: 'icon-[solar--gamepad-broken] text-xl', path: '/type/8' },
    { label: '科技', icon: 'icon-[streamline--ai-science-spark] text-xl', path: '/type/9' },
    { label: '动画', icon: 'icon-[ri--bilibili-line] text-xl', path: '/type/10' }
])

const logout = () => {
    dialog.open(Tips, {
        data: { text: '确认退出登录？' },
        onClose: ({ data }) => {
            if (data) userStore.logout().then(() => router.push('/'))
        }
    })
}
</script>

<template>
    <div class="relative flex basis-[10%] justify-center overflow-hidden rounded-2xl bg-white/50 text-sm">
        <div
            :class="[userStore.isLogin ? 'h-[calc(100%-5rem)]' : 'h-[calc(100%-1rem)]']"
            class="absolute mt-2 flex w-3/4 flex-col gap-2 overflow-y-auto scrollbar-hide">
            <div
                v-for="item in items"
                @click="item.requireLogin && !userStore.isLogin ? null : router.push(item.path).then(()=>router.go(0))"
                :class="{
                    '!bg-sky-300': item.path === route.path,
                    'opacity-50 cursor-not-allowed': item.requireLogin && !userStore.isLogin
                }"
                class="flex cursor-pointer items-center justify-center gap-2 rounded-2xl py-3 hover:bg-sky-200">
                <i :class="item.icon" />
                <span>{{ item.label }}</span>
            </div>
        </div>
        <div
            v-if="userStore.isLogin"
            @click="logout"
            class="absolute bottom-0 flex h-16 w-full cursor-pointer items-center justify-center gap-2 bg-sky-300">
            <i class="icon-[prime--sign-out] text-xl" />
            <span>退出登录</span>
        </div>
    </div>
</template>

<style scoped></style>
