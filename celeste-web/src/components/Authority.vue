<script setup>
import { useUserStore } from '@/stores/model/user.js'
import { every, includes } from 'lodash-es'
import { computed } from 'vue'

const props = defineProps(['authorities'])

const userStore = useUserStore()

const show = computed(() => every(props.authorities, (authority) => includes(userStore.user.authorities, authority)))
</script>

<template>
    <div v-if="userStore.isLogin && show">
        <slot />
    </div>
</template>

<style scoped></style>
