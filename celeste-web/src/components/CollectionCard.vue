<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
    collection: {
        type: Object,
        required: true
    }
})

const emit = defineEmits(['click'])

const handleClick = () => {
    emit('click', props.collection)
}
</script>

<template>
    <div 
        @click="handleClick"
        class="group relative cursor-pointer overflow-hidden rounded-lg bg-white shadow-md transition-all duration-300 hover:shadow-lg hover:scale-105"
    >
        <!-- 收藏夹封面 -->
        <div class="relative h-32 bg-gradient-to-br from-sky-400 to-blue-500 flex items-center justify-center">
            <div class="text-center text-white">
                <i class="icon-[flowbite--star-solid] text-4xl mb-2" />
                <p class="text-sm opacity-90">{{ collection.videosSum }} 个视频</p>
            </div>
            
            <!-- 悬停效果 -->
            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300" />
        </div>
        
        <!-- 收藏夹信息 -->
        <div class="p-4">
            <h3 class="font-semibold text-gray-800 truncate mb-1">
                {{ collection.name }}
            </h3>
            <p class="text-sm text-gray-500">
                {{ collection.betweenTime || '最近更新' }}
            </p>
        </div>
        
        <!-- 播放按钮 -->
        <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <div class="bg-white bg-opacity-90 rounded-full p-2 shadow-md">
                <i class="icon-[flowbite--play-solid] text-sky-500 text-lg" />
            </div>
        </div>
    </div>
</template>

<style scoped>
/* 可以添加额外的样式 */
</style>
