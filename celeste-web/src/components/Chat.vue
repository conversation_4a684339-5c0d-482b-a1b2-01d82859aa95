<script setup>
import { computed } from 'vue'
import Image from '@/components/Image.vue'

const props = defineProps({
    right: {
        type: Boolean,
        default: false
    },
    message: {
        type: Object,
        required: true,
        validator: (value) => {
            return value && typeof value.avatar === 'string'
        }
    }
})

// 计算消息时间显示
const formatTime = (timeString) => {
    if (!timeString) return ''
    const date = new Date(timeString)
    const now = new Date()
    const diff = now - date

    if (diff < 60000) { // 1分钟内
        return '刚刚'
    } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 1天内
        return `${Math.floor(diff / 3600000)}小时前`
    } else {
        return date.toLocaleDateString()
    }
}

// 解析图片数组
const imageList = computed(() => {
    if (!props.message.images) return []
    try {
        return typeof props.message.images === 'string'
            ? JSON.parse(props.message.images)
            : props.message.images
    } catch (e) {
        return []
    }
})
</script>

<template>
    <div
        :class="{ 'flex-row-reverse': right }"
        class="flex items-start gap-3 mb-4 max-w-full"
    >
        <!-- 头像 -->
        <div class="flex-shrink-0">
            <img
                :src="message.avatar"
                :alt="right ? '我' : '对方'"
                class="w-10 h-10 rounded-full object-cover border-2 border-gray-200"
                @error="$event.target.src = '/default-avatar.svg'"
            />
        </div>

        <!-- 消息内容 -->
        <div
            :class="{ 'items-end': right, 'items-start': !right }"
            class="flex flex-col max-w-xs sm:max-w-md lg:max-w-lg"
        >
            <!-- 消息气泡 -->
            <div
                :class="{
                    'bg-blue-500 text-white': right,
                    'bg-white text-gray-800 border border-gray-200': !right
                }"
                class="rounded-2xl px-4 py-2 shadow-sm relative"
            >
                <!-- 文本内容 -->
                <div v-if="message.content" class="text-sm leading-relaxed break-words">
                    {{ message.content }}
                </div>

                <!-- 图片内容 -->
                <div
                    v-if="imageList.length > 0"
                    :class="{ 'mt-2': message.content }"
                    class="grid gap-2"
                    :style="{
                        gridTemplateColumns: imageList.length === 1
                            ? '1fr'
                            : 'repeat(auto-fit, minmax(120px, 1fr))'
                    }"
                >
                    <Image
                        v-for="(image, index) in imageList"
                        :key="index"
                        :src="image"
                        :alt="`图片 ${index + 1}`"
                        preview
                        class="rounded-lg cursor-pointer hover:opacity-90 transition-opacity max-w-full h-auto"
                        :class="{
                            'max-h-48': imageList.length === 1,
                            'max-h-32': imageList.length > 1
                        }"
                        @click="$emit('image-click', image)"
                    />
                </div>

                <!-- 消息气泡尾巴 -->
                <div
                    :class="{
                        'right-0 border-l-blue-500': right,
                        'left-0 border-r-white': !right
                    }"
                    class="absolute top-3 w-0 h-0 border-t-8 border-b-8 border-t-transparent border-b-transparent"
                    :style="{
                        borderLeftWidth: right ? '8px' : '0',
                        borderRightWidth: !right ? '8px' : '0',
                        borderLeftColor: right ? '#3b82f6' : 'transparent',
                        borderRightColor: !right ? '#ffffff' : 'transparent',
                        transform: right ? 'translateX(100%)' : 'translateX(-100%)'
                    }"
                />
            </div>

            <!-- 时间戳 -->
            <div
                v-if="message.createTime"
                :class="{ 'text-right': right }"
                class="text-xs text-gray-400 mt-1 px-2"
            >
                {{ formatTime(message.createTime) }}
            </div>
        </div>
    </div>
</template>

<style scoped>
/* 确保图片不会超出容器 */
img {
    max-width: 100%;
    height: auto;
}

/* 消息气泡动画 */
.rounded-2xl {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 悬停效果 */
.cursor-pointer:hover {
    transform: scale(1.02);
    transition: transform 0.2s ease;
}
</style>
