import { request } from '@/utils/service.js'

// 用户管理相关API

/**
 * 获取所有用户（分页）
 */
export const getAllUsersApi = (page = 0, size = 20) => {
    return request({
        url: '/api/admin/users',
        method: 'GET',
        params: { page, size }
    })
}

/**
 * 搜索用户
 */
export const searchUsersApi = (keyword) => {
    return request({
        url: '/api/admin/users/search',
        method: 'GET',
        params: { keyword }
    })
}

/**
 * 禁用用户
 */
export const disableUserApi = (userId) => {
    return request({
        url: `/api/admin/users/${userId}/disable`,
        method: 'POST'
    })
}

/**
 * 启用用户
 */
export const enableUserApi = (userId) => {
    return request({
        url: `/api/admin/users/${userId}/enable`,
        method: 'POST'
    })
}

// 视频管理相关API

/**
 * 获取所有视频（分页）
 */
export const getAllVideosApi = (page = 0, size = 20) => {
    return request({
        url: '/api/admin/videos',
        method: 'GET',
        params: { page, size }
    })
}

/**
 * 搜索视频
 */
export const searchVideosApi = (keyword) => {
    return request({
        url: '/api/admin/videos/search',
        method: 'GET',
        params: { keyword }
    })
}

/**
 * 下架视频
 */
export const removeVideoApi = (videoId) => {
    return request({
        url: `/api/admin/videos/${videoId}/remove`,
        method: 'POST'
    })
}

/**
 * 恢复视频
 */
export const restoreVideoApi = (videoId) => {
    return request({
        url: `/api/admin/videos/${videoId}/restore`,
        method: 'POST'
    })
}

// 统计信息API

/**
 * 获取管理员统计信息
 */
export const getAdminStatisticsApi = () => {
    return request({
        url: '/api/admin/statistics',
        method: 'GET'
    })
}
