import { defineStore } from 'pinia'
import { ref, reactive, computed } from 'vue'
import { getRecentChatsApi, getChatHistoryApi } from '@/api/messageApi.js'

export const useChatStore = defineStore(
    'chat',
    () => {
        // 当前聊天对象
        const currentChat = ref(null)

        // 聊天列表（最近联系人）
        const chatList = ref([])

        // 消息列表 - 按聊天对象分组
        const messagesByChat = reactive({})

        // 未读消息计数
        const unreadCounts = reactive({})

        // 是否正在加载
        const loading = ref(false)

        // 计算属性：当前聊天的消息
        const currentMessages = computed(() => {
            if (!currentChat.value) return []
            const chatKey = getChatKey(currentChat.value.id)
            const messages = messagesByChat[chatKey] || []

            // 按时间排序，最新的在下面
            return messages.slice().sort((a, b) => {
                const timeA = new Date(a.createTime).getTime()
                const timeB = new Date(b.createTime).getTime()
                return timeA - timeB
            })
        })

        // 计算属性：总未读消息数
        const totalUnreadCount = computed(() => {
            return Object.values(unreadCounts).reduce((total, count) => total + count, 0)
        })

        // 生成聊天键值
        const getChatKey = (otherUserId) => {
            return `chat_${otherUserId}`
        }

        // 设置当前聊天对象
        const setCurrentChat = (user, currentUserId = null) => {
            currentChat.value = user
            if (user) {
                // 清除未读消息计数
                unreadCounts[user.id] = 0

                // 如果是新用户（不在聊天列表中），添加到聊天列表
                if (currentUserId) {
                    const existingChat = chatList.value.find(chat => {
                        const senderId = chat.senderId || chat.sender?.id
                        const recipientId = chat.recipientId || chat.recipient?.id

                        return (senderId === user.id && recipientId === currentUserId) ||
                               (senderId === currentUserId && recipientId === user.id)
                    })

                    if (!existingChat) {
                        // 创建一个虚拟的聊天记录用于显示在列表中
                        const virtualChat = {
                            id: `virtual_${user.id}`,
                            sender: { id: currentUserId, username: 'current_user' },
                            recipient: user,
                            senderId: currentUserId,
                            recipientId: user.id,
                            content: '',
                            images: null,
                            createTime: new Date().toISOString()
                        }
                        chatList.value.unshift(virtualChat)
                    }
                }
            }
        }

        // 获取聊天列表
        const fetchChatList = async () => {
            try {
                loading.value = true
                const response = await getRecentChatsApi()

                // 前端去重逻辑：确保每个对话伙伴只出现一次
                const uniqueChats = []
                const seenPartners = new Set()

                if (response && Array.isArray(response)) {
                    response.forEach(chat => {
                        // 确定对话伙伴ID（不是当前用户的那个）
                        const currentUserId = getCurrentUserId()
                        const partnerId = chat.senderId === currentUserId
                            ? chat.recipientId
                            : chat.senderId

                        // 如果这个对话伙伴还没有出现过，添加到列表中
                        if (!seenPartners.has(partnerId)) {
                            seenPartners.add(partnerId)
                            uniqueChats.push(chat)
                        }
                    })
                }

                chatList.value = uniqueChats
                return uniqueChats
            } catch (error) {
                console.error('获取聊天列表失败:', error)
                return []
            } finally {
                loading.value = false
            }
        }

        // 当前用户ID缓存
        let currentUserId = null

        // 设置当前用户ID
        const setCurrentUserId = (userId) => {
            currentUserId = userId
        }

        // 获取当前用户ID的辅助函数
        const getCurrentUserId = () => {
            return currentUserId
        }

        // 获取聊天历史
        const fetchChatHistory = async (otherUserId, page = 0, size = 20) => {
            try {
                loading.value = true
                const response = await getChatHistoryApi(otherUserId, page, size)

                const chatKey = getChatKey(otherUserId)
                const currentUserId = getCurrentUserId()

                // 修复历史消息数据
                const fixMessage = (msg) => {
                    // 如果senderId和recipientId都相同，需要修复
                    if (msg.senderId === msg.recipientId) {
                        // 暂时假设所有这样的消息都是当前用户发送的
                        // 这是一个临时解决方案，直到后端修复
                        return {
                            ...msg,
                            senderId: currentUserId,
                            recipientId: otherUserId
                        }
                    }
                    return msg
                }

                if (page === 0) {
                    // 第一页，替换现有消息，修复数据并按时间排序
                    const messages = (response.rows || []).map(fixMessage)
                    messagesByChat[chatKey] = messages.sort((a, b) => {
                        const timeA = new Date(a.createTime).getTime()
                        const timeB = new Date(b.createTime).getTime()
                        return timeA - timeB
                    })
                } else {
                    // 后续页，追加到现有消息前面，修复数据并重新排序
                    const existing = messagesByChat[chatKey] || []
                    const newMessages = (response.rows || []).map(fixMessage)
                    const allMessages = [...existing, ...newMessages]
                    messagesByChat[chatKey] = allMessages.sort((a, b) => {
                        const timeA = new Date(a.createTime).getTime()
                        const timeB = new Date(b.createTime).getTime()
                        return timeA - timeB
                    })
                }

                return response
            } catch (error) {
                console.error('获取聊天历史失败:', error)
                return { rows: [], totalPageCount: 0 }
            } finally {
                loading.value = false
            }
        }

        // 添加消息（接收到新消息时）
        const addMessage = (message) => {
            console.log('🔍 addMessage 调试:')
            console.log('- 接收到的消息:', message)

            const currentUserId = getCurrentUserId()

            // 如果消息的senderId和recipientId都相同且不合理，跳过处理
            if (message.senderId === message.recipientId) {
                console.log('- 跳过处理：senderId和recipientId相同')
                return
            }

            // 确定对话伙伴ID
            const otherUserId = message.senderId === currentUserId ?
                message.recipientId : message.senderId
            const chatKey = getChatKey(otherUserId)

            console.log('- currentUserId:', currentUserId)
            console.log('- otherUserId:', otherUserId)
            console.log('- chatKey:', chatKey)

            if (!messagesByChat[chatKey]) {
                messagesByChat[chatKey] = []
            }

            // 检查消息是否已存在（避免重复）
            const exists = messagesByChat[chatKey].some(msg => msg.id === message.id)
            if (!exists) {
                messagesByChat[chatKey].push(message)

                // 重新排序消息
                messagesByChat[chatKey].sort((a, b) => {
                    const timeA = new Date(a.createTime).getTime()
                    const timeB = new Date(b.createTime).getTime()
                    return timeA - timeB
                })

                // 如果不是当前聊天，增加未读计数
                if (currentChat.value?.id !== otherUserId) {
                    unreadCounts[otherUserId] = (unreadCounts[otherUserId] || 0) + 1
                }
            }
        }

        // 确认消息发送（发送消息后的确认）
        const confirmMessage = (message) => {
            console.log('🔍 confirmMessage 调试:')
            console.log('- 接收到的消息:', message)
            console.log('- message.senderId:', message.senderId, typeof message.senderId)
            console.log('- message.recipientId:', message.recipientId, typeof message.recipientId)

            // 临时修复：确保senderId是当前用户ID，recipientId是对话伙伴ID
            const currentUserId = getCurrentUserId()

            // 确定正确的接收者ID（应该是当前聊天对象的ID）
            const correctRecipientId = currentChat.value?.id

            const correctedMessage = {
                ...message,
                senderId: currentUserId,
                recipientId: correctRecipientId || message.recipientId
            }

            console.log('- 修正后的消息:', correctedMessage)

            const otherUserId = correctedMessage.recipientId
            const chatKey = getChatKey(otherUserId)

            if (!messagesByChat[chatKey]) {
                messagesByChat[chatKey] = []
            }

            // 找到并移除对应的待发送消息
            const pendingIndex = messagesByChat[chatKey].findIndex(msg =>
                msg.pending &&
                msg.content === correctedMessage.content &&
                msg.recipientId === correctedMessage.recipientId
            )

            if (pendingIndex !== -1) {
                // 移除待发送消息
                messagesByChat[chatKey].splice(pendingIndex, 1)
            }

            // 检查消息是否已存在
            const exists = messagesByChat[chatKey].some(msg => msg.id === correctedMessage.id)
            if (!exists) {
                messagesByChat[chatKey].push(correctedMessage)

                // 重新排序消息
                messagesByChat[chatKey].sort((a, b) => {
                    const timeA = new Date(a.createTime).getTime()
                    const timeB = new Date(b.createTime).getTime()
                    return timeA - timeB
                })
            }
        }

        // 发送消息（临时添加到界面，等待确认）
        const addPendingMessage = (recipientId, content, images = null) => {
            const chatKey = getChatKey(recipientId)

            if (!messagesByChat[chatKey]) {
                messagesByChat[chatKey] = []
            }

            const currentUserId = getCurrentUserId()

            const pendingMessage = {
                id: `pending_${Date.now()}`,
                senderId: currentUserId, // 使用实际的当前用户ID
                recipientId,
                content,
                images,
                createTime: new Date().toISOString(),
                pending: true
            }

            messagesByChat[chatKey].push(pendingMessage)

            // 重新排序消息
            messagesByChat[chatKey].sort((a, b) => {
                const timeA = new Date(a.createTime).getTime()
                const timeB = new Date(b.createTime).getTime()
                return timeA - timeB
            })

            return pendingMessage
        }

        // 移除待发送消息
        const removePendingMessage = (messageId) => {
            Object.keys(messagesByChat).forEach(chatKey => {
                const messages = messagesByChat[chatKey]
                const index = messages.findIndex(msg => msg.id === messageId)
                if (index !== -1) {
                    messages.splice(index, 1)
                }
            })
        }

        // 清空聊天数据
        const clearChatData = () => {
            currentChat.value = null
            chatList.value = []
            Object.keys(messagesByChat).forEach(key => {
                delete messagesByChat[key]
            })
            Object.keys(unreadCounts).forEach(key => {
                delete unreadCounts[key]
            })
        }

        return {
            // 状态
            currentChat,
            chatList,
            messagesByChat,
            unreadCounts,
            loading,

            // 计算属性
            currentMessages,
            totalUnreadCount,

            // 方法
            setCurrentChat,
            setCurrentUserId,
            fetchChatList,
            fetchChatHistory,
            addMessage,
            confirmMessage,
            addPendingMessage,
            removePendingMessage,
            clearChatData,
            getChatKey
        }
    },
    {
        persist: {
            paths: ['chatList', 'unreadCounts']
        }
    }
)
