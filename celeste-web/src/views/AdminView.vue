<script setup>
import { ref, onMounted, computed, h } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/model/user.js'
import {
    getAllUsersApi,
    searchUsersApi,
    disableUser<PERSON>pi,
    enableUserApi,
    getAllVideosApi,
    searchVideosApi,
    removeVideoApi,
    restoreVideoApi,
    getAdminStatisticsApi
} from '@/api/adminApi.js'
import { toast } from '@/components/toast/index.js'
import { NTabs, NTabPane, NDataTable, NButton, NInput, NSpace, NCard, NStatistic, NGrid, NGridItem } from 'naive-ui'

const router = useRouter()
const userStore = useUserStore()

// 检查管理员权限
const isAdmin = computed(() => {
    console.log('Checking admin permission:', userStore.user) // 调试日志
    return userStore.user?.roleList?.includes('ADMIN') || false
})

// 临时注释掉重定向，用于调试
// if (!isAdmin.value) {
//     router.push('/')
// }

// 数据状态
const users = ref([])
const videos = ref([])
const statistics = ref({})
const loading = ref(false)
const searchKeyword = ref('')
const activeTab = ref('statistics')

// 用户表格列定义
const userColumns = [
    {
        title: 'ID',
        key: 'id',
        width: 80
    },
    {
        title: '用户名',
        key: 'username',
        width: 120
    },
    {
        title: '邮箱',
        key: 'email',
        width: 200
    },
    {
        title: '性别',
        key: 'gender',
        width: 80
    },
    {
        title: '状态',
        key: 'status',
        width: 100,
        render(row) {
            const status = row.status || 'ACTIVE'
            return h('span', {
                class: status === 'ACTIVE' ? 'text-green-600' : 'text-red-600'
            }, status === 'ACTIVE' ? '正常' : '禁用')
        }
    },
    {
        title: '关注数',
        key: 'adoresSum',
        width: 80
    },
    {
        title: '粉丝数',
        key: 'followersSum',
        width: 80
    },
    {
        title: '注册时间',
        key: 'betweenTime',
        width: 120
    },
    {
        title: '操作',
        key: 'actions',
        width: 150,
        render(row) {
            const status = row.status || 'ACTIVE'
            return h(NSpace, null, {
                default: () => [
                    h(NButton, {
                        size: 'small',
                        type: status === 'ACTIVE' ? 'error' : 'success',
                        onClick: () => toggleUserStatus(row)
                    }, { default: () => status === 'ACTIVE' ? '禁用' : '启用' })
                ]
            })
        }
    }
]

// 视频表格列定义
const videoColumns = [
    {
        title: 'ID',
        key: 'id',
        width: 80
    },
    {
        title: '描述',
        key: 'description',
        width: 200,
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '作者',
        key: 'user.username',
        width: 120
    },
    {
        title: '分类',
        key: 'type.name',
        width: 100
    },
    {
        title: '状态',
        key: 'status',
        width: 100,
        render(row) {
            const status = row.status || 'ACTIVE'
            return h('span', {
                class: status === 'ACTIVE' ? 'text-green-600' : 'text-red-600'
            }, status === 'ACTIVE' ? '正常' : '已下架')
        }
    },
    {
        title: '点赞数',
        key: 'likesSum',
        width: 80
    },
    {
        title: '评论数',
        key: 'commentsSum',
        width: 80
    },
    {
        title: '收藏数',
        key: 'collectionsSum',
        width: 80
    },
    {
        title: '发布时间',
        key: 'betweenTime',
        width: 120
    },
    {
        title: '操作',
        key: 'actions',
        width: 150,
        render(row) {
            const status = row.status || 'ACTIVE'
            return h(NSpace, null, {
                default: () => [
                    h(NButton, {
                        size: 'small',
                        type: status === 'ACTIVE' ? 'error' : 'success',
                        onClick: () => toggleVideoStatus(row)
                    }, { default: () => status === 'ACTIVE' ? '下架' : '恢复' })
                ]
            })
        }
    }
]

// 方法
const loadStatistics = async () => {
    try {
        loading.value = true
        const data = await getAdminStatisticsApi()
        statistics.value = data
    } catch (error) {
        console.error('获取统计信息失败:', error)
        toast.error('获取统计信息失败')
    } finally {
        loading.value = false
    }
}

const loadUsers = async () => {
    try {
        loading.value = true
        const data = await getAllUsersApi(0, 100) // 获取前100个用户
        users.value = data.content || data
    } catch (error) {
        console.error('获取用户列表失败:', error)
        toast.error('获取用户列表失败')
    } finally {
        loading.value = false
    }
}

const loadVideos = async () => {
    try {
        loading.value = true
        const data = await getAllVideosApi(0, 100) // 获取前100个视频
        videos.value = data.content || data
    } catch (error) {
        console.error('获取视频列表失败:', error)
        toast.error('获取视频列表失败')
    } finally {
        loading.value = false
    }
}

const searchUsers = async () => {
    if (!searchKeyword.value.trim()) {
        await loadUsers()
        return
    }

    try {
        loading.value = true
        const data = await searchUsersApi(searchKeyword.value)
        users.value = data
    } catch (error) {
        console.error('搜索用户失败:', error)
        toast.error('搜索用户失败')
    } finally {
        loading.value = false
    }
}

const searchVideos = async () => {
    if (!searchKeyword.value.trim()) {
        await loadVideos()
        return
    }

    try {
        loading.value = true
        const data = await searchVideosApi(searchKeyword.value)
        videos.value = data
    } catch (error) {
        console.error('搜索视频失败:', error)
        toast.error('搜索视频失败')
    } finally {
        loading.value = false
    }
}

const toggleUserStatus = async (user) => {
    try {
        const currentStatus = user.status || 'ACTIVE'
        if (currentStatus === 'ACTIVE') {
            await disableUserApi(user.id)
            user.status = 'DISABLED'
            toast.success('用户已禁用')
        } else {
            await enableUserApi(user.id)
            user.status = 'ACTIVE'
            toast.success('用户已启用')
        }
    } catch (error) {
        console.error('操作失败:', error)
        toast.error('操作失败')
    }
}

const toggleVideoStatus = async (video) => {
    try {
        const currentStatus = video.status || 'ACTIVE'
        if (currentStatus === 'ACTIVE') {
            await removeVideoApi(video.id)
            video.status = 'REMOVED'
            toast.success('视频已下架')
        } else {
            await restoreVideoApi(video.id)
            video.status = 'ACTIVE'
            toast.success('视频已恢复')
        }
    } catch (error) {
        console.error('操作失败:', error)
        toast.error('操作失败')
    }
}

const handleTabChange = (value) => {
    activeTab.value = value
    searchKeyword.value = ''

    if (value === 'users') {
        loadUsers()
    } else if (value === 'videos') {
        loadVideos()
    } else if (value === 'statistics') {
        loadStatistics()
    }
}

const handleSearch = () => {
    if (activeTab.value === 'users') {
        searchUsers()
    } else if (activeTab.value === 'videos') {
        searchVideos()
    }
}

onMounted(() => {
    loadStatistics()
})
</script>

<template>
    <div>
        <!-- 调试信息 -->
        <div class="mb-4 p-4 bg-gray-100 rounded">
            <h3>调试信息:</h3>
            <p>用户登录状态: {{ userStore.isLogin }}</p>
            <p>用户信息: {{ JSON.stringify(userStore.user) }}</p>
            <p>是否管理员: {{ isAdmin }}</p>
        </div>

        <div v-if="true || isAdmin" class="flex basis-[90%] flex-col overflow-hidden rounded-2xl bg-white/50">
        <div class="flex h-16 items-center justify-between border-b-2 border-b-sky-500 bg-white/50 px-5">
            <h1 class="text-2xl font-bold text-gray-800">管理员面板</h1>
        </div>

        <NTabs
            v-model:value="activeTab"
            type="line"
            @update:value="handleTabChange"
            class="flex-1 p-5">

            <!-- 统计信息标签页 -->
            <NTabPane name="statistics" tab="统计信息">
                <NGrid cols="2 s:1 m:2 l:3 xl:3 2xl:3" responsive="screen" :x-gap="16" :y-gap="16">
                    <NGridItem>
                        <NCard title="用户统计">
                            <NSpace vertical>
                                <NStatistic label="总用户数" :value="statistics.totalUsers || 0" />
                                <NStatistic label="活跃用户" :value="statistics.activeUsers || 0" />
                                <NStatistic label="禁用用户" :value="statistics.disabledUsers || 0" />
                            </NSpace>
                        </NCard>
                    </NGridItem>

                    <NGridItem>
                        <NCard title="视频统计">
                            <NSpace vertical>
                                <NStatistic label="总视频数" :value="statistics.totalVideos || 0" />
                                <NStatistic label="正常视频" :value="statistics.activeVideos || 0" />
                                <NStatistic label="下架视频" :value="statistics.removedVideos || 0" />
                            </NSpace>
                        </NCard>
                    </NGridItem>
                </NGrid>
            </NTabPane>

            <!-- 用户管理标签页 -->
            <NTabPane name="users" tab="用户管理">
                <div class="space-y-4">
                    <div class="flex gap-4">
                        <NInput
                            v-model:value="searchKeyword"
                            placeholder="搜索用户名或邮箱..."
                            class="flex-1"
                            @keyup.enter="handleSearch" />
                        <NButton type="primary" @click="handleSearch">搜索</NButton>
                        <NButton @click="loadUsers">刷新</NButton>
                    </div>

                    <NDataTable
                        :columns="userColumns"
                        :data="users"
                        :loading="loading"
                        :pagination="{ pageSize: 20 }"
                        :scroll-x="1000" />
                </div>
            </NTabPane>

            <!-- 视频管理标签页 -->
            <NTabPane name="videos" tab="视频管理">
                <div class="space-y-4">
                    <div class="flex gap-4">
                        <NInput
                            v-model:value="searchKeyword"
                            placeholder="搜索视频描述..."
                            class="flex-1"
                            @keyup.enter="handleSearch" />
                        <NButton type="primary" @click="handleSearch">搜索</NButton>
                        <NButton @click="loadVideos">刷新</NButton>
                    </div>

                    <NDataTable
                        :columns="videoColumns"
                        :data="videos"
                        :loading="loading"
                        :pagination="{ pageSize: 20 }"
                        :scroll-x="1200" />
                </div>
            </NTabPane>
        </NTabs>
        </div>

        <div v-else class="flex grow flex-col items-center justify-center gap-5">
            <p class="text-xl font-bold">访问被拒绝</p>
            <p class="text-gray-500">您没有管理员权限</p>
            <NButton @click="router.push('/')">返回首页</NButton>
        </div>
    </div>
</template>

<style scoped>
/* 可以添加额外的样式 */
</style>
