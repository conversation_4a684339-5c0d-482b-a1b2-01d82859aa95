{"name": "celeste-web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@iconify/json": "^2.2.240", "@primevue/themes": "^4.0.5", "@vueuse/core": "^11.0.3", "axios": "^1.7.5", "dayjs": "^1.11.13", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "naive-ui": "^2.39.0", "pinia": "^2.2.2", "pinia-plugin-persistedstate": "^3.2.1", "primevue": "^4.0.5", "socket.io-client": "^4.8.1", "swiper": "^11.1.10", "vee-validate": "^4.13.2", "vue": "^3.4.38", "vue-router": "^4.4.3", "vue3-lottie": "^3.3.0", "xgplayer": "^3.0.20", "yup": "^1.4.0"}, "devDependencies": {"@iconify/tailwind": "^1.1.3", "@primevue/auto-import-resolver": "^4.0.5", "@tailwindcss/container-queries": "^0.1.1", "@vitejs/plugin-vue": "^5.1.1", "autoprefixer": "^10.4.20", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss": "^3.4.10", "tailwindcss-primeui": "^0.3.4", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.2"}}